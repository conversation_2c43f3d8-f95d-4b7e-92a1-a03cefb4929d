source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.3.2'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails', '~> 7.0.8.4'
# Use sqlite3 as the database for Active Record
gem 'pg', '~> 1.3'
# Use Puma as the app server
gem 'puma', '~> 5.6'

# Use Redis adapter to run Action Cable in production
# gem 'redis', '~> 4.0'
gem 'activerecord-import'
# Use Active Model has_secure_password
gem 'activerecord-postgres_enum'
gem 'aws-sdk-s3'
gem 'bcrypt', '~> 3.1.7'
gem 'elasticsearch-model', '~> 7.2', '>= 7.2.1'
gem 'elasticsearch-persistence', '~> 7.2', '>= 7.2.1'
gem 'elasticsearch-rails', '~> 7.2', '>= 7.2.1'
gem 'faraday'
gem 'faraday-httpclient'
gem 'google-api-client'
gem 'google-cloud-storage', '~> 1.56'
gem 'google-id-token'
gem 'jwt'
gem 'kaminari'
gem 'pg_search'
gem 'poppler'
gem 'psych', '< 4'
gem 'redis'
gem 'redis-rails'
gem 'ruby-openai', '~> 7.1.0'
gem 'seedbank'
gem 'sidekiq', '~> 6.5.10'
gem 'sidekiq-cron', '~> 1.5.0'
gem 'with_advisory_lock'
# Use Active Storage variant
# gem 'image_processing', '~> 1.2'

# gem 'scout_apm'
# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.4.2', require: false
gem 'discard', '~> 1.2'
gem 'figaro'
gem 'honeybadger', '~> 4.0'
gem 'mixpanel-ruby'
gem 'rack-cors'
gem 'request_store', '~> 1.5'
gem 'request_store-sidekiq', '0.1.0'
gem 'sentry-rails', '~> 4.8', '>= 4.8.1'
gem 'sentry-ruby', '~> 4.8', '>= 4.8.1'
gem 'sentry-sidekiq', '~> 4.8', '>= 4.8.1'

# Multitenancy for Rails and ActiveRecord
gem 'ros-apartment', require: 'apartment'

gem 'after_commit_everywhere'

gem 'event_stream_parser', '~> 1.0'
gem 'neighbor'
gem 'google-cloud-bigquery'

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'active_record_query_trace'
  gem 'byebug', platforms: [:mri, :mingw, :x64_mingw]
  gem 'mock_redis'
  gem 'pry-rails'
  gem 'rspec-json_expectations'
  gem 'rspec-rails', '~> 4.0.2'
  gem 'rspec-sqlimit'
end

group :development do
  gem 'bcrypt_pbkdf'
  gem 'listen', '~> 3.2'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
  gem 'spring-watcher-listen', '~> 2.0.0'

  gem 'ed25519'
  gem 'letter_opener'

  gem 'rubocop', '~> 1.69.2', require: false
  gem 'rubocop-performance'
  gem 'rubocop-rails'
  gem 'rubocop-rspec'
end

group :test do
  gem 'database_cleaner'
  gem 'dox', require: false
  gem 'factory_bot_rails', '~> 4.0'
  gem 'faker'
  gem 'shoulda-matchers', '~> 4.0'
  gem 'timecop'
end

group :staging, :production do
  # Lograge
  gem 'lograge'
  gem 'lograge-sql', '~> 2.5.1'

  # Monitoring
  gem 'opentelemetry-exporter-otlp'
  gem 'opentelemetry-instrumentation-aws_sdk'
  gem 'opentelemetry-instrumentation-faraday'
  gem 'opentelemetry-instrumentation-http_client'
  gem 'opentelemetry-instrumentation-net_http'
  gem 'opentelemetry-instrumentation-pg'
  gem 'opentelemetry-instrumentation-rack'
  gem 'opentelemetry-instrumentation-rails'
  gem 'opentelemetry-instrumentation-redis'
  gem 'opentelemetry-instrumentation-sidekiq'
  gem 'opentelemetry-sdk'
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]
