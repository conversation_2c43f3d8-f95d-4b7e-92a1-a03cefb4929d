# frozen_string_literal: true

class AddColumnsToExperiences < ActiveRecord::Migration[7.0]
  def change
    add_reference :experiences, :partner, foreign_key: true, null: true
    add_reference :experiences, :job_level, foreign_key: true, null: true
    add_reference :experiences, :partner_placement, foreign_key: true, null: true
    add_reference :experiences, :promoted_from, foreign_key: { to_table: :experiences }, null: true
    add_column :experiences, :division, :string
  end
end
