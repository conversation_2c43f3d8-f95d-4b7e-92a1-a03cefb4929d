# frozen_string_literal: true

class CreateUserPartnerCompetencyHistories < ActiveRecord::Migration[7.0]
  def change
    create_table :user_partner_competency_histories do |t|
      t.references :user_partner_competency, foreign_key: true, null: false, index: {
        name: 'idx_upch_on_user_partner_competency_id'
      }

      t.date :evaluation_start_date
      t.date :evaluation_end_date
      t.float :score

      t.timestamps
      t.datetime :discarded_at, index: true
    end
  end
end
