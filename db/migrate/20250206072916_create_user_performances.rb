# frozen_string_literal: true

class CreateUserPerformances < ActiveRecord::Migration[7.0]
  def change
    create_table :user_performances do |t|
      t.belongs_to :user
      t.string :performance_type
      t.text :objective
      t.text :detail
      t.float :completion_achievement
      t.string :period_type
      t.datetime :starts_at
      t.datetime :ends_at

      t.timestamps
    end
  end
end
