# frozen_string_literal: true

class CreatePartnerCompetencies < ActiveRecord::Migration[7.0]
  def change
    create_table :partner_competencies do |t|
      t.references :partner, foreign_key: true, null: false
      t.string :name
      t.text :description
      t.string :core_value
      t.float :eligible_target
      t.integer :level

      t.timestamps
      t.datetime :discarded_at, index: true
    end
  end
end
