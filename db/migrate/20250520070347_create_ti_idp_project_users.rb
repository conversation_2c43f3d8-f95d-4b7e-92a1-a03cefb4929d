# frozen_string_literal: true

class CreateTiIdpProjectUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :ti_idp_project_users do |t|
      t.references :ti_idp_project, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.jsonb :competency_gaps, array: true, null: false, default: []
      t.string :objectives, array: true, null: false, default: []
      t.jsonb :ai_recommendation, null: false, default: {}
      t.string :status, null: false, default: :queued

      t.datetime :discarded_at, index: true
      t.timestamps
    end
  end
end
