# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_05_23_074704) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_trgm"
  enable_extension "plpgsql"
  enable_extension "vector"

  create_enum :answer_type, [
    "essay",
    "uploaded_document",
  ], force: :cascade

  create_enum :applied_type, [
    "bootcamp",
    "short_course",
    "internal",
    "trial",
    "jgp",
    "virtual_working_experience",
    "unlimited",
    "offline",
    "hybrid",
    "prakerja",
    "job_acceleration",
    "iq_test",
  ], force: :cascade

  create_enum :assessment_type, [
    "career",
    "personality",
  ], force: :cascade

  create_enum :assignment_status, [
    "submitted",
    "not_submitted",
    "scoring",
    "on_time",
    "late",
  ], force: :cascade

  create_enum :competency_type, [
    "ds",
    "dm",
  ], force: :cascade

  create_enum :course_difficulty, [
    "beginner",
    "intermediate",
    "advance",
  ], force: :cascade

  create_enum :course_visibility, [
    "public",
    "private",
  ], force: :cascade

  create_enum :difficulty, [
    "beginner",
    "intermediate",
    "advance",
  ], force: :cascade

  create_enum :event_type, [
    "attend",
    "upload_content",
    "remind_tutor",
    "upload_material",
    "complete_assignment",
    "vix_batch",
    "mentoring",
    "jap_onboarding",
    "voucher_limited_date",
    "bootcamp_trial",
  ], force: :cascade

  create_enum :installment_request_status, [
    "waiting_for_approval",
    "approved",
    "rejected",
    "withdrawn",
  ], force: :cascade

  create_enum :installment_status, [
    "active",
    "stopped",
    "paused",
    "waiting",
    "completed",
    "cancelled",
  ], force: :cascade

  create_enum :interval_type, [
    "day",
    "week",
    "month",
  ], force: :cascade

  create_enum :invoice_status, [
    "pending",
    "paid",
    "expired",
    "withdrawn",
  ], force: :cascade

  create_enum :level, [
    "basic",
    "novice",
    "intermediate",
    "advanced",
    "expert",
  ], force: :cascade

  create_enum :missed_payment_action, [
    "ignore",
    "stop",
  ], force: :cascade

  create_enum :module_session_state, [
    "draft",
    "published",
  ], force: :cascade

  create_enum :option_type, [
    "multiple_choice",
    "checkbox",
  ], force: :cascade

  create_enum :payment_channel, [
    "bca",
    "bni",
    "bni_syariah",
    "bri",
    "mandiri",
    "permata",
    "dana",
    "ovo",
    "linkaja",
    "shopeepay",
    "gcash",
    "grabpay",
    "paymaya",
    "qris",
    "alfamart",
    "indomaret",
    "credit_card",
  ], force: :cascade

  create_enum :payment_method, [
    "bank_transfer",
    "credit_card",
    "retail_outlet",
    "ewallet",
    "qris",
    "qr_code",
  ], force: :cascade

  create_enum :promo_type, [
    "voucher",
    "referral",
    "promo",
    "jgp",
    "limited_promo",
    "redemption_code",
  ], force: :cascade

  create_enum :satisfaction_type, [
    "frown",
    "flat",
    "smile",
    "excited",
  ], force: :cascade

  create_enum :score_category, [
    "low",
    "high",
  ], force: :cascade

  create_enum :session_type, [
    "reading",
    "homework",
    "exam",
    "last_project",
    "video",
    "introduction",
    "live_session",
    "reading_assignment",
    "mentoring",
    "trial_test",
    "verbal",
    "numeric",
    "instruction_test",
    "survey",
    "verbal_comprehension",
    "word_fluency",
    "inductive_reasoning",
    "numerical_ability",
    "spatial_relations",
    "memory",
    "perceptual_speed",
    "ai_interview",
    "real_test",
    "post_test",
    "pre_test",
  ], force: :cascade

  create_enum :status, [
    "attended",
    "missed",
    "permitted",
  ], force: :cascade

  create_enum :status_availability, [
    "open_to_work",
    "closed",
    "looking_job",
    "applied",
    "happy_with_current_company",
    "freshgrads",
    "final_year_student",
    "not_selected",
  ], force: :cascade

  create_enum :status_completed, [
    "not_started",
    "in_progress",
    "completed",
    "timeout",
    "not_passed",
  ], force: :cascade

  create_enum :survey_component_type, [
    "intro_page",
    "essay",
    "scale",
    "dropdown",
    "checkbox",
    "multiple_choice",
    "upload_file",
    "date_picker",
    "statement_info",
    "thank_you_page",
  ], force: :cascade

  create_enum :survey_option_type, [
    "level",
    "checkbox",
    "multiple_choice",
    "dropdown",
    "scale",
  ], force: :cascade

  create_enum :survey_target_type, [
    "student",
    "tutor",
    "tutor_and_admin",
  ], force: :cascade

  create_enum :survey_type, [
    "pre_class",
    "on_class",
    "post_class",
    "post_module",
    "profile",
  ], force: :cascade

  create_enum :user_course_state, [
    "enrolled",
    "completed",
    "cancelled",
    "applied",
    "expired",
  ], force: :cascade

  create_enum :user_role, [
    "student",
    "tutor",
    "course_coordinator",
    "consultant",
    "admin",
    "counselor",
  ], force: :cascade

  create_enum :user_survey_state, [
    "in_progress",
    "done",
  ], force: :cascade

  create_enum :wishlist_state, [
    "active",
    "deleted",
  ], force: :cascade

  create_enum :work_type, [
    "full_time",
    "intern",
    "part_time",
    "contract",
    "freelance",
  ], force: :cascade

  create_table "achievement_competencies", force: :cascade do |t|
    t.bigint "achievement_id"
    t.bigint "competency_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["achievement_id", "competency_id"], name: "index_unique_achievement_id_competency_id", unique: true
    t.index ["achievement_id"], name: "index_achievement_competencies_on_achievement_id"
    t.index ["competency_id"], name: "index_achievement_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_achievement_competencies_on_discarded_at"
  end

  create_table "achievements", force: :cascade do |t|
    t.string "title"
    t.datetime "start_date", precision: nil
    t.datetime "end_date", precision: nil
    t.string "location"
    t.text "description"
    t.bigint "experience_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.bigint "education_id"
    t.jsonb "achievement_certification", default: {"rank"=>nil, "type"=>nil, "level"=>nil, "participant"=>nil, "certificate_url"=>nil}, null: false
    t.bigint "user_id"
    t.bigint "course_id"
    t.boolean "public_access_status", default: true
    t.bigint "license_id"
    t.index ["course_id"], name: "index_achievements_on_course_id"
    t.index ["discarded_at"], name: "index_achievements_on_discarded_at"
    t.index ["education_id"], name: "index_achievements_on_education_id"
    t.index ["experience_id"], name: "index_achievements_on_experience_id"
    t.index ["license_id"], name: "index_achievements_on_license_id"
    t.index ["user_id"], name: "index_achievements_on_user_id"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", precision: nil, null: false
    t.string "service_name", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "addresses", force: :cascade do |t|
    t.text "address"
    t.string "district"
    t.string "ward"
    t.string "postal_code"
    t.string "address_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.bigint "location_id"
    t.bigint "province_id"
    t.bigint "user_id"
    t.integer "public_location_id"
    t.index ["address_type"], name: "index_addresses_on_address_type"
    t.index ["discarded_at"], name: "index_addresses_on_discarded_at"
    t.index ["location_id"], name: "index_addresses_on_location_id"
    t.index ["province_id"], name: "index_addresses_on_province_id"
    t.index ["public_location_id"], name: "index_addresses_on_public_location_id"
    t.index ["user_id"], name: "index_addresses_on_user_id"
  end

  create_table "ai_assistant_runs", force: :cascade do |t|
    t.bigint "ai_assistant_id"
    t.bigint "user_id"
    t.string "run_id"
    t.string "thread_id"
    t.string "status"
    t.string "file_url"
    t.text "raw_response"
    t.jsonb "json_response", default: {}, null: false
    t.integer "total_token"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "request_raw"
    t.index ["ai_assistant_id"], name: "index_ai_assistant_runs_on_ai_assistant_id"
    t.index ["discarded_at"], name: "index_ai_assistant_runs_on_discarded_at"
    t.index ["run_id"], name: "index_ai_assistant_runs_on_run_id", unique: true
    t.index ["user_id"], name: "index_ai_assistant_runs_on_user_id"
  end

  create_table "ai_assistants", force: :cascade do |t|
    t.string "assistant_id", null: false
    t.string "purpose", null: false
    t.string "content"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "provider", default: "openai"
    t.string "status", default: "active"
    t.text "system_instruction"
    t.text "model"
    t.jsonb "config", default: {}, null: false
    t.index ["assistant_id"], name: "index_ai_assistants_on_assistant_id", unique: true
    t.index ["discarded_at"], name: "index_ai_assistants_on_discarded_at"
  end

  create_table "ai_chats", force: :cascade do |t|
    t.bigint "ai_prompt_id", null: false
    t.bigint "user_id", null: false
    t.string "title"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "resolve_status"
    t.index ["ai_prompt_id"], name: "index_ai_chats_on_ai_prompt_id"
    t.index ["discarded_at"], name: "index_ai_chats_on_discarded_at"
    t.index ["title"], name: "index_ai_chats_on_title"
    t.index ["user_id"], name: "index_ai_chats_on_user_id"
  end

  create_table "ai_contexts", force: :cascade do |t|
    t.bigint "ai_prompt_id", null: false
    t.string "content"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.vector "embedding", limit: 1536
    t.integer "total_tokens", default: 0
    t.index ["ai_prompt_id"], name: "index_ai_contexts_on_ai_prompt_id"
    t.index ["discarded_at"], name: "index_ai_contexts_on_discarded_at"
    t.index ["embedding"], name: "index_ai_contexts_on_embedding", opclass: :vector_l2_ops, using: :hnsw
  end

  create_table "ai_messages", force: :cascade do |t|
    t.bigint "ai_chat_id", null: false
    t.string "content"
    t.string "sender", null: false
    t.integer "total_token"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "input_tokens"
    t.integer "output_tokens"
    t.string "label"
    t.index ["ai_chat_id"], name: "index_ai_messages_on_ai_chat_id"
    t.index ["discarded_at"], name: "index_ai_messages_on_discarded_at"
    t.index ["sender"], name: "index_ai_messages_on_sender"
  end

  create_table "ai_prompts", force: :cascade do |t|
    t.string "name", null: false
    t.string "model", default: "gpt-3.5-turbo", null: false
    t.string "message", null: false
    t.float "temperature", default: 1.0, null: false
    t.integer "top_p", default: 1, null: false
    t.integer "max_token", default: 3000, null: false
    t.integer "presence_penalty", default: 0, null: false
    t.integer "frequency_penalty", default: 0, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "config", default: {}
    t.index ["discarded_at"], name: "index_ai_prompts_on_discarded_at"
    t.index ["message"], name: "index_ai_prompts_on_message"
    t.index ["model"], name: "index_ai_prompts_on_model"
    t.index ["name"], name: "index_ai_prompts_on_name", unique: true
  end

  create_table "answer_feedbacks", force: :cascade do |t|
    t.text "feedback"
    t.string "video_url"
    t.string "reference_urls", array: true
    t.string "attachment_url"
    t.datetime "discarded_at", precision: nil
    t.bigint "homework_question_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_answer_feedbacks_on_discarded_at"
    t.index ["homework_question_id"], name: "index_answer_feedbacks_on_homework_question_id"
  end

  create_table "answer_options", force: :cascade do |t|
    t.bigint "homework_option_id"
    t.bigint "user_id"
    t.float "score"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_answer_options_on_discarded_at"
    t.index ["homework_option_id"], name: "index_answer_options_on_homework_option_id"
    t.index ["user_id", "homework_option_id"], name: "index_answer_options_on_user_id_and_homework_option_id", unique: true
    t.index ["user_id"], name: "index_answer_options_on_user_id"
  end

  create_table "answers", force: :cascade do |t|
    t.text "answer_text"
    t.enum "answer_type", enum_type: "answer_type"
    t.float "score"
    t.bigint "homework_question_id"
    t.bigint "user_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "document_url"
    t.datetime "assessed_at", precision: nil
    t.index ["discarded_at"], name: "index_answers_on_discarded_at"
    t.index ["homework_question_id"], name: "index_answers_on_homework_question_id"
    t.index ["user_id", "homework_question_id"], name: "index_answers_on_user_id_and_homework_question_id", unique: true
    t.index ["user_id"], name: "index_answers_on_user_id"
  end

  create_table "assessment_answers", force: :cascade do |t|
    t.integer "score", default: 0
    t.bigint "user_id"
    t.bigint "assessment_question_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["assessment_question_id"], name: "index_assessment_answers_on_assessment_question_id"
    t.index ["discarded_at"], name: "index_assessment_answers_on_discarded_at"
    t.index ["user_id", "assessment_question_id"], name: "index_assessment_answers_on_user_id_and_assessment_question_id", unique: true
    t.index ["user_id"], name: "index_assessment_answers_on_user_id"
  end

  create_table "assessment_aspects", force: :cascade do |t|
    t.text "name"
    t.string "key"
    t.string "external_id"
    t.enum "assessment_type", enum_type: "assessment_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "config", default: {}
    t.datetime "discarded_at", precision: nil
    t.text "description"
    t.text "relevant_skill"
    t.index ["discarded_at"], name: "index_assessment_aspects_on_discarded_at"
    t.index ["external_id"], name: "index_assessment_aspects_on_external_id", unique: true
    t.index ["key", "assessment_type"], name: "index_assessment_aspects_on_key_and_assessment_type", unique: true
  end

  create_table "assessment_feedbacks", force: :cascade do |t|
    t.text "description"
    t.enum "satisfaction_type", enum_type: "satisfaction_type"
    t.datetime "discarded_at", precision: nil
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_assessment_feedbacks_on_discarded_at"
    t.index ["user_id"], name: "index_assessment_feedbacks_on_user_id"
    t.index ["user_id"], name: "index_unique_assessment_feedbacks_on_user_id", unique: true
  end

  create_table "assessment_items", force: :cascade do |t|
    t.bigint "sub_competency_id", null: false
    t.bigint "job_role_id", null: false
    t.bigint "bloom_taxonomy_id", null: false
    t.string "specialization"
    t.string "category", null: false
    t.string "item_type", null: false
    t.string "item_label", null: false
    t.string "question", null: false
    t.string "quality", null: false
    t.float "angoff"
    t.float "aiken"
    t.float "crit"
    t.float "item_diff"
    t.float "item_disc"
    t.string "text_answer_keys", default: [], array: true
    t.jsonb "answer_options", default: []
    t.string "answer_detail", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "competency_id", null: false
    t.bigint "specialization_id"
    t.index ["aiken"], name: "index_assessment_items_on_aiken"
    t.index ["angoff"], name: "index_assessment_items_on_angoff"
    t.index ["bloom_taxonomy_id"], name: "index_assessment_items_on_bloom_taxonomy_id"
    t.index ["category"], name: "index_assessment_items_on_category"
    t.index ["competency_id"], name: "index_assessment_items_on_competency_id"
    t.index ["crit"], name: "index_assessment_items_on_crit"
    t.index ["item_diff"], name: "index_assessment_items_on_item_diff"
    t.index ["item_disc"], name: "index_assessment_items_on_item_disc"
    t.index ["item_label"], name: "index_assessment_items_on_item_label"
    t.index ["item_type"], name: "index_assessment_items_on_item_type"
    t.index ["job_role_id"], name: "index_assessment_items_on_job_role_id"
    t.index ["quality"], name: "index_assessment_items_on_quality"
    t.index ["question"], name: "index_assessment_items_on_question"
    t.index ["specialization"], name: "index_assessment_items_on_specialization"
    t.index ["specialization_id"], name: "index_assessment_items_on_specialization_id"
    t.index ["sub_competency_id"], name: "index_assessment_items_on_sub_competency_id"
  end

  create_table "assessment_personas", force: :cascade do |t|
    t.string "name"
    t.string "external_id"
    t.text "description"
    t.enum "score_category", enum_type: "score_category"
    t.integer "score_starts_at", default: 0
    t.integer "score_ends_at", default: 0
    t.bigint "assessment_aspect_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["assessment_aspect_id"], name: "index_assessment_personas_on_assessment_aspect_id"
    t.index ["discarded_at"], name: "index_assessment_personas_on_discarded_at"
    t.index ["external_id"], name: "index_assessment_personas_on_external_id", unique: true
    t.index ["score_category", "assessment_aspect_id"], name: "index_assessment_personas", unique: true
  end

  create_table "assessment_questions", force: :cascade do |t|
    t.text "question"
    t.string "external_id"
    t.enum "assessment_type", enum_type: "assessment_type"
    t.integer "order_level", default: 0
    t.bigint "assessment_aspect_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["assessment_aspect_id"], name: "index_assessment_questions_on_assessment_aspect_id"
    t.index ["discarded_at"], name: "index_assessment_questions_on_discarded_at"
    t.index ["external_id"], name: "index_assessment_questions_on_external_id", unique: true
  end

  create_table "assessment_results", force: :cascade do |t|
    t.bigint "user_id"
    t.string "assessment_name"
    t.jsonb "result"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_assessment_results_on_discarded_at"
    t.index ["user_id"], name: "index_assessment_results_on_user_id"
  end

  create_table "assessment_sub_scores", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "course_id", null: false
    t.float "score", default: 0.0
    t.string "sub_score_type"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "percentile", default: 0.0
    t.string "object_type"
    t.bigint "object_id"
    t.jsonb "config", default: {}, null: false
    t.jsonb "score_details"
    t.index ["course_id", "user_id", "object_id", "object_type"], name: "index_sub_score_type_unique", unique: true
    t.index ["course_id"], name: "index_assessment_sub_scores_on_course_id"
    t.index ["discarded_at"], name: "index_assessment_sub_scores_on_discarded_at"
    t.index ["object_type", "object_id"], name: "index_assessment_sub_scores_on_object_type_and_object_id"
    t.index ["user_id", "course_id", "sub_score_type"], name: "index_sub_score_user_id_course_id_type", unique: true
    t.index ["user_id"], name: "index_assessment_sub_scores_on_user_id"
  end

  create_table "assessment_sub_type_facets", force: :cascade do |t|
    t.string "name", null: false
    t.string "parameterized_name", null: false
    t.bigint "assessment_sub_type_id", null: false
    t.jsonb "config", default: {}, null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assessment_sub_type_id", "parameterized_name"], name: "index_assessment_sub_type_facets_on_ast_id_and_param_name", unique: true
    t.index ["assessment_sub_type_id"], name: "index_assessment_sub_type_facets_on_assessment_sub_type_id"
    t.index ["discarded_at"], name: "index_assessment_sub_type_facets_on_discarded_at"
  end

  create_table "assessment_sub_types", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "config", default: {}, null: false
    t.string "parameterized_name"
    t.index ["discarded_at"], name: "index_assessment_sub_types_on_discarded_at"
    t.index ["parameterized_name"], name: "index_assessment_sub_types_on_parameterized_name", unique: true
  end

  create_table "assignment_scores", force: :cascade do |t|
    t.enum "status", enum_type: "assignment_status"
    t.float "score"
    t.bigint "user_id"
    t.bigint "module_session_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "email_sent", default: false
    t.datetime "discarded_at", precision: nil
    t.bigint "vwx_task_component_id"
    t.bigint "final_scoring_metric_id"
    t.bigint "final_scoring_sub_metric_id"
    t.bigint "final_scoring_verificator_id"
    t.text "feedback"
    t.jsonb "feedback_image_urls", default: []
    t.bigint "feedback_by_id"
    t.float "correct_answers"
    t.float "wrong_answers"
    t.float "blank_answers"
    t.string "feedback_url", default: ""
    t.index ["discarded_at"], name: "index_assignment_scores_on_discarded_at"
    t.index ["feedback_by_id"], name: "index_assignment_scores_on_feedback_by_id"
    t.index ["final_scoring_metric_id", "user_id"], name: "index_assignment_scores_on_metric_id_and_user_id", unique: true
    t.index ["final_scoring_metric_id"], name: "index_assignment_scores_on_final_scoring_metric_id"
    t.index ["final_scoring_sub_metric_id", "user_id"], name: "index_assignment_scores_on_sub_metric_id_and_user_id", unique: true
    t.index ["final_scoring_sub_metric_id"], name: "index_assignment_scores_on_final_scoring_sub_metric_id"
    t.index ["final_scoring_verificator_id", "user_id"], name: "index_assignment_scores_on_verificator_id_and_user_id", unique: true
    t.index ["final_scoring_verificator_id"], name: "index_assignment_scores_on_final_scoring_verificator_id"
    t.index ["module_session_id"], name: "index_assignment_scores_on_module_session_id"
    t.index ["user_id", "module_session_id"], name: "index_assignment_scores_on_user_id_and_module_session_id", unique: true
    t.index ["user_id", "vwx_task_component_id"], name: "index_assignment_scores_on_user_id_and_vwx_task_component_id", unique: true
    t.index ["user_id"], name: "index_assignment_scores_on_user_id"
    t.index ["vwx_task_component_id"], name: "index_assignment_scores_on_vwx_task_component_id"
  end

  create_table "ast_taggings", force: :cascade do |t|
    t.string "ast_object_type", null: false
    t.integer "ast_object_id", null: false
    t.integer "ast_object_order", default: 0, null: false
    t.string "target_object_type", null: false
    t.integer "target_object_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ast_object_type", "ast_object_id", "ast_object_order", "target_object_type", "target_object_id"], name: "index_ast_taggings_on_ast_object_and_target_object", unique: true
    t.index ["discarded_at"], name: "index_ast_taggings_on_discarded_at"
  end

  create_table "attendance_histories", force: :cascade do |t|
    t.enum "status", enum_type: "status"
    t.bigint "user_id"
    t.bigint "module_session_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "permit_reason"
    t.boolean "reminder_sent", default: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_attendance_histories_on_discarded_at"
    t.index ["module_session_id"], name: "index_attendance_histories_on_module_session_id"
    t.index ["user_id", "module_session_id"], name: "index_attendance_histories_on_user_id_and_module_session_id", unique: true
    t.index ["user_id"], name: "index_attendance_histories_on_user_id"
  end

  create_table "bank_accounts", force: :cascade do |t|
    t.string "bank_account_number"
    t.string "bank_name"
    t.string "account_name"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_bank_accounts_on_user_id"
  end

  create_table "behaviour_scorings", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "module_session_id"
    t.bigint "behaviour_id"
    t.bigint "mentoring_group_id"
    t.integer "score", default: 0
    t.integer "week"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["behaviour_id"], name: "index_behaviour_scorings_on_behaviour_id"
    t.index ["mentoring_group_id"], name: "index_behaviour_scorings_on_mentoring_group_id"
    t.index ["module_session_id"], name: "index_behaviour_scorings_on_module_session_id"
    t.index ["user_id"], name: "index_behaviour_scorings_on_user_id"
  end

  create_table "behaviours", force: :cascade do |t|
    t.bigint "scoring_template_id"
    t.string "name"
    t.string "description"
    t.integer "weight", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_behaviours_on_discarded_at"
    t.index ["scoring_template_id"], name: "index_behaviours_on_scoring_template_id"
  end

  create_table "bloom_taxonomies", force: :cascade do |t|
    t.string "name"
    t.string "framework_competency_type"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "order_level", default: 0, null: false
    t.text "description"
    t.integer "total_need", default: 0
    t.index ["discarded_at"], name: "index_bloom_taxonomies_on_discarded_at"
  end

  create_table "bundling_codes", force: :cascade do |t|
    t.string "name", default: "A", null: false
    t.bigint "module_session_id", null: false
    t.integer "assigned_students_count", default: 0, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_bundling_codes_on_discarded_at"
    t.index ["module_session_id"], name: "index_bundling_codes_on_module_session_id"
    t.index ["name", "module_session_id"], name: "index_bundling_codes_on_name_and_module_session_id", unique: true
  end

  create_table "career_paths", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.bigint "job_role_id"
    t.float "target_score", default: 0.0
    t.text "target_audience", default: [], array: true
    t.string "path_image_url"
    t.jsonb "tier_image_url", default: {"gold"=>nil, "bronze"=>nil, "silver"=>nil}, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "features", default: []
    t.text "mastered_skill_description"
    t.text "skillset_needed"
    t.text "career_chance"
    t.text "role_description"
    t.jsonb "skill_list", default: []
    t.string "typeform_tech_assessment_url"
    t.jsonb "target", default: []
    t.jsonb "learning_milestone", default: {}
    t.string "employability_test_url"
    t.index ["discarded_at"], name: "index_career_paths_on_discarded_at"
    t.index ["job_role_id"], name: "index_career_paths_on_job_role_id"
  end

  create_table "cc_assessment_sub_types", force: :cascade do |t|
    t.bigint "course_config_id", null: false
    t.bigint "assessment_sub_type_id", null: false
    t.jsonb "config", default: {}, null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assessment_sub_type_id"], name: "index_cc_assessment_sub_types_on_assessment_sub_type_id"
    t.index ["course_config_id", "assessment_sub_type_id"], name: "index_cc_assessment_sub_types_on_cc_id_and_ast_id", unique: true
    t.index ["course_config_id"], name: "index_cc_assessment_sub_types_on_course_config_id"
    t.index ["discarded_at"], name: "index_cc_assessment_sub_types_on_discarded_at"
  end

  create_table "cc_ast_facets", force: :cascade do |t|
    t.bigint "cc_assessment_sub_type_id", null: false
    t.bigint "assessment_sub_type_facet_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assessment_sub_type_facet_id"], name: "index_cc_ast_facets_on_assessment_sub_type_facet_id"
    t.index ["cc_assessment_sub_type_id", "assessment_sub_type_facet_id"], name: "index_cc_ast_facets_on_cc_ast_id_and_ast_facet_id", unique: true
    t.index ["cc_assessment_sub_type_id"], name: "index_cc_ast_facets_on_cc_assessment_sub_type_id"
    t.index ["discarded_at"], name: "index_cc_ast_facets_on_discarded_at"
  end

  create_table "certificate_templates", force: :cascade do |t|
    t.string "title"
    t.string "description"
    t.boolean "show_course"
    t.boolean "show_specialization"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "template_type"
    t.string "course_detail"
    t.index ["title", "template_type"], name: "index_certificate_templates_on_title_and_template_type", unique: true
  end

  create_table "certificates", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "course_id"
    t.datetime "discarded_at", precision: nil
    t.string "document_url"
    t.index ["course_id"], name: "index_certificates_on_course_id"
    t.index ["discarded_at"], name: "index_certificates_on_discarded_at"
    t.index ["user_id"], name: "index_certificates_on_user_id"
  end

  create_table "company_assessment_bills", force: :cascade do |t|
    t.bigint "user_job_vacancy_assessment_id", null: false
    t.datetime "email_sent_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_company_assessment_bills_on_discarded_at"
    t.index ["email_sent_at"], name: "index_company_assessment_bills_on_email_sent_at"
    t.index ["user_job_vacancy_assessment_id"], name: "index_company_assessment_bills_on_ujva_id", unique: true
  end

  create_table "company_assessment_templates", force: :cascade do |t|
    t.bigint "module_session_id"
    t.bigint "job_role_group_id"
    t.string "access_tier", default: "free", null: false
    t.string "company_assessment_type", default: "general_assessment", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "description", default: ""
    t.bigint "course_id"
    t.bigint "partner_id"
    t.jsonb "config", default: {}, null: false
    t.integer "total_questions", default: 0, null: false
    t.string "status", default: "active"
    t.index ["course_id"], name: "index_company_assessment_templates_on_course_id"
    t.index ["discarded_at"], name: "index_company_assessment_templates_on_discarded_at"
    t.index ["job_role_group_id"], name: "index_company_assessment_templates_on_job_role_group_id"
    t.index ["module_session_id"], name: "index_company_assessment_templates_on_module_session_id"
    t.index ["partner_id"], name: "index_company_assessment_templates_on_partner_id"
  end

  create_table "company_registration_requests", force: :cascade do |t|
    t.string "name"
    t.string "job_title"
    t.string "email"
    t.string "phone_number"
    t.string "company_name"
    t.string "company_size"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_company_registration_requests_on_discarded_at"
  end

  create_table "competencies", force: :cascade do |t|
    t.string "name"
    t.enum "competency_type", enum_type: "competency_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "job_role_id", default: 0, null: false
    t.datetime "discarded_at", precision: nil
    t.float "eligible_target", default: 0.0, null: false
    t.bigint "bloom_taxonomy_max_level_id"
    t.bigint "career_path_id"
    t.string "external_id"
    t.bigint "bootcamp_level_id"
    t.bigint "vix_level_id"
    t.boolean "verified", default: true, null: false
    t.text "description", default: "", null: false
    t.index ["bloom_taxonomy_max_level_id"], name: "index_competencies_on_bloom_taxonomy_max_level_id"
    t.index ["bootcamp_level_id"], name: "index_competencies_on_bootcamp_level_id"
    t.index ["career_path_id"], name: "index_competencies_on_career_path_id"
    t.index ["discarded_at"], name: "index_competencies_on_discarded_at"
    t.index ["external_id"], name: "index_competencies_on_external_id", unique: true
    t.index ["job_role_id"], name: "index_competencies_on_job_role_id"
    t.index ["name", "job_role_id"], name: "index_competencies_on_name_and_job_role_id", unique: true
    t.index ["vix_level_id"], name: "index_competencies_on_vix_level_id"
  end

  create_table "consultation_hirings", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.string "phone_number"
    t.string "company_name"
    t.string "service_type"
    t.string "role"
    t.string "role_in_company"
    t.string "total_employee_type"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "other_role_value"
    t.index ["discarded_at"], name: "index_consultation_hirings_on_discarded_at"
  end

  create_table "consultation_trainings", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.string "phone_number"
    t.string "company_name"
    t.string "company_type"
    t.string "trained_competency"
    t.string "custom_trained_competency"
    t.string "training_type"
    t.datetime "starts_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_consultation_trainings_on_discarded_at"
  end

  create_table "course_configs", force: :cascade do |t|
    t.string "course_type", null: false
    t.jsonb "report_config", default: {}, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "label"
    t.jsonb "default_values", default: {}, null: false
    t.index ["course_type"], name: "index_course_configs_on_course_type", unique: true
    t.index ["default_values"], name: "index_course_configs_on_default_values", using: :gin
    t.index ["report_config"], name: "index_course_configs_on_report_config", using: :gin
  end

  create_table "course_modules", force: :cascade do |t|
    t.string "name"
    t.integer "order_level"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "course_id"
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.text "description"
    t.string "state", default: "upcoming"
    t.string "module_type", default: "main"
    t.index ["course_id"], name: "index_course_modules_on_course_id"
    t.index ["discarded_at"], name: "index_course_modules_on_discarded_at"
    t.index ["module_type"], name: "index_course_modules_on_module_type"
    t.index ["state"], name: "index_course_modules_on_state"
  end

  create_table "course_reviews", force: :cascade do |t|
    t.bigint "course_id", null: false
    t.float "average_rating", default: 0.0, null: false
    t.jsonb "review_count", default: {"1"=>0, "2"=>0, "3"=>0, "4"=>0, "5"=>0, "overall"=>0}, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id"], name: "index_course_reviews_on_course_id", unique: true
    t.index ["discarded_at"], name: "index_course_reviews_on_discarded_at"
  end

  create_table "course_rewards", force: :cascade do |t|
    t.integer "exp_required"
    t.boolean "special_reward"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "course_id", null: false
    t.bigint "reward_id", null: false
    t.integer "quota"
    t.integer "used_quota", default: 0
    t.index ["course_id"], name: "index_course_rewards_on_course_id"
    t.index ["discarded_at"], name: "index_course_rewards_on_discarded_at"
    t.index ["reward_id"], name: "index_course_rewards_on_reward_id"
  end

  create_table "course_vwxes", force: :cascade do |t|
    t.bigint "course_id"
    t.bigint "vwx_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id", "vwx_id"], name: "index_course_vwxes_on_course_id_and_vwx_id", unique: true
    t.index ["course_id"], name: "index_course_vwxes_on_course_id"
    t.index ["vwx_id"], name: "index_course_vwxes_on_vwx_id"
  end

  create_table "courses", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.float "price", default: 0.0, null: false
    t.string "whatsapp_group_url"
    t.float "minimum_score", default: 0.0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "session", default: 0
    t.integer "modul", default: 0
    t.string "category"
    t.string "benefit", default: [], array: true
    t.jsonb "composition", default: {}
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.enum "difficulty", enum_type: "course_difficulty"
    t.enum "visibility", default: "public", enum_type: "course_visibility"
    t.integer "maximum_student"
    t.bigint "scoring_template_id"
    t.string "organizer_name"
    t.string "course_type"
    t.float "external_score", default: 0.0
    t.integer "partner_id"
    t.string "picture_url"
    t.string "image_partner_url"
    t.string "code"
    t.string "specialization"
    t.string "batch"
    t.bigint "major_id"
    t.string "tutor_companies", default: [], array: true
    t.jsonb "schedules", default: []
    t.jsonb "features", default: []
    t.float "discount_percentage", default: 0.0, null: false
    t.float "duration", default: 0.0
    t.datetime "early_bird_date", precision: nil
    t.string "video_url"
    t.integer "course_modules_count", default: 0
    t.integer "module_sessions_count", default: 0
    t.integer "user_courses_count", default: 0
    t.string "course_group_url", default: ""
    t.integer "rakamin_point", default: 0, null: false
    t.string "video_thumbnail_url"
    t.bigint "job_role_id"
    t.integer "related_course_ids", default: [], array: true
    t.bigint "university_id"
    t.jsonb "config", default: {}
    t.string "competency"
    t.jsonb "certificate_competencies", default: {}
    t.string "specific_category"
    t.text "certificate_description"
    t.jsonb "course_details"
    t.index ["course_type"], name: "index_courses_on_course_type"
    t.index ["discarded_at"], name: "index_courses_on_discarded_at"
    t.index ["job_role_id"], name: "index_courses_on_job_role_id"
    t.index ["major_id"], name: "index_courses_on_major_id"
    t.index ["scoring_template_id"], name: "index_courses_on_scoring_template_id"
    t.index ["starts_at"], name: "index_courses_on_starts_at"
    t.index ["university_id"], name: "index_courses_on_university_id"
    t.index ["visibility"], name: "index_courses_on_visibility"
  end

  create_table "education_levels", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.integer "order_level", default: 0
    t.index ["discarded_at"], name: "index_education_levels_on_discarded_at"
  end

  create_table "educations", force: :cascade do |t|
    t.string "school"
    t.string "degree"
    t.string "major"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "start_date", precision: nil, null: false
    t.datetime "end_date", precision: nil
    t.text "description"
    t.datetime "discarded_at", precision: nil
    t.float "score", default: 0.0, null: false
    t.bigint "education_level_id"
    t.bigint "university_id"
    t.bigint "university_major_id"
    t.integer "semester"
    t.string "education_certificate_url"
    t.string "education_transcripts_url"
    t.bigint "location_id"
    t.string "education_certificate_number"
    t.integer "public_location_id"
    t.integer "pub_univ_major_id"
    t.index ["discarded_at"], name: "index_educations_on_discarded_at"
    t.index ["education_level_id"], name: "index_educations_on_education_level_id"
    t.index ["location_id"], name: "index_educations_on_location_id"
    t.index ["pub_univ_major_id"], name: "index_educations_on_pub_univ_major_id"
    t.index ["public_location_id"], name: "index_educations_on_public_location_id"
    t.index ["university_id"], name: "index_educations_on_university_id"
    t.index ["university_major_id"], name: "index_educations_on_university_major_id"
    t.index ["user_id"], name: "index_educations_on_user_id"
  end

  create_table "experience_competencies", force: :cascade do |t|
    t.bigint "experience_id"
    t.bigint "competency_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competency_id"], name: "index_experience_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_experience_competencies_on_discarded_at"
    t.index ["experience_id", "competency_id"], name: "index_unique_experience_id_competency_id", unique: true
    t.index ["experience_id"], name: "index_experience_competencies_on_experience_id"
  end

  create_table "experience_industry_categories", force: :cascade do |t|
    t.bigint "experience_id", null: false
    t.bigint "industry_category_id", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_experience_industry_categories_on_discarded_at"
    t.index ["experience_id", "industry_category_id"], name: "index_unique_experience_id_industry_category_id", unique: true
    t.index ["experience_id"], name: "index_experience_industry_categories_on_experience_id"
    t.index ["industry_category_id"], name: "index_experience_industry_categories_on_industry_category_id"
  end

  create_table "experiences", force: :cascade do |t|
    t.string "company_name"
    t.datetime "starts_at", precision: nil, null: false
    t.datetime "ends_at", precision: nil
    t.text "description"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "role_name"
    t.enum "work_type", enum_type: "work_type"
    t.string "location"
    t.datetime "discarded_at", precision: nil
    t.string "company_phone_number"
    t.string "company_logo_url"
    t.string "employment_certificate_url"
    t.bigint "job_role_id"
    t.boolean "public_access_status", default: true
    t.bigint "vwx_id"
    t.string "quit_reason"
    t.bigint "partner_id"
    t.bigint "job_level_id"
    t.bigint "partner_placement_id"
    t.bigint "promoted_from_id"
    t.string "division"
    t.index ["discarded_at"], name: "index_experiences_on_discarded_at"
    t.index ["job_level_id"], name: "index_experiences_on_job_level_id"
    t.index ["job_role_id"], name: "index_experiences_on_job_role_id"
    t.index ["partner_id"], name: "index_experiences_on_partner_id"
    t.index ["partner_placement_id"], name: "index_experiences_on_partner_placement_id"
    t.index ["promoted_from_id"], name: "index_experiences_on_promoted_from_id"
    t.index ["user_id"], name: "index_experiences_on_user_id"
    t.index ["vwx_id"], name: "index_experiences_on_vwx_id"
  end

  create_table "external_rewards", force: :cascade do |t|
    t.bigint "reward_id"
    t.bigint "user_id"
    t.string "code"
    t.datetime "expired_date", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_external_rewards_on_discarded_at"
    t.index ["reward_id"], name: "index_external_rewards_on_reward_id"
    t.index ["user_id"], name: "index_external_rewards_on_user_id"
  end

  create_table "feature_permissions", force: :cascade do |t|
    t.string "feature", default: "ai_mentor", null: false
    t.jsonb "config", default: {}, null: false
    t.boolean "eligible", default: false, null: false
    t.bigint "user_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_feature_permissions_on_discarded_at"
    t.index ["feature", "user_id"], name: "index_feature_permissions_on_feature_and_user_id", unique: true
    t.index ["feature"], name: "index_feature_permissions_on_feature"
    t.index ["user_id"], name: "index_feature_permissions_on_user_id"
  end

  create_table "features", force: :cascade do |t|
    t.string "name", null: false
    t.jsonb "properties", default: {}, null: false
    t.datetime "release_date", precision: nil, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "value"
    t.index ["discarded_at"], name: "index_features_on_discarded_at"
    t.index ["name"], name: "index_features_on_name", unique: true
  end

  create_table "feedback_templates", force: :cascade do |t|
    t.string "feedback_type", null: false
    t.string "title"
    t.string "subtitle"
    t.jsonb "config", default: {}, null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_feedback_templates_on_discarded_at"
    t.index ["feedback_type"], name: "index_feedback_templates_on_feedback_type", unique: true
  end

  create_table "final_scoring_metrics", force: :cascade do |t|
    t.bigint "course_id"
    t.string "metric_type"
    t.string "title"
    t.float "weight"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id"], name: "index_final_scoring_metrics_on_course_id"
  end

  create_table "final_scoring_sub_metrics", force: :cascade do |t|
    t.bigint "final_scoring_metric_id"
    t.string "metric_type"
    t.string "title"
    t.float "weight"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "course_id", null: false
    t.index ["course_id"], name: "index_final_scoring_sub_metrics_on_course_id"
    t.index ["final_scoring_metric_id"], name: "index_final_scoring_sub_metrics_on_final_scoring_metric_id"
  end

  create_table "final_scoring_verificators", force: :cascade do |t|
    t.float "value"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "object_type"
    t.bigint "object_id"
    t.string "verification_type", default: "metric_score"
    t.index ["object_type", "object_id"], name: "index_final_scoring_verificators_on_object_type_and_object_id"
  end

  create_table "final_scorings", force: :cascade do |t|
    t.float "report_score", default: 0.0
    t.float "presentation_score", default: 0.0
    t.float "contribution_score", default: 0.0
    t.float "final_score", default: 0.0
    t.bigint "user_project_id"
    t.bigint "scoring_summary_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "course_id"
    t.integer "user_id"
    t.index ["scoring_summary_id"], name: "index_final_scorings_on_scoring_summary_id"
    t.index ["user_id", "course_id"], name: "index_final_scorings_on_user_id_and_course_id", unique: true
    t.index ["user_project_id", "course_id"], name: "index_final_scorings_on_user_project_id_and_course_id", unique: true
    t.index ["user_project_id"], name: "index_final_scorings_on_user_project_id"
  end

  create_table "gcalendar_events", force: :cascade do |t|
    t.string "summary"
    t.text "description"
    t.string "event_class"
    t.integer "event_class_id"
    t.string "event_type"
    t.string "event_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.index ["discarded_at"], name: "index_gcalendar_events_on_discarded_at"
    t.index ["ends_at"], name: "index_gcalendar_events_on_ends_at"
    t.index ["event_class", "event_class_id"], name: "index_gcalendar_events_on_event_class_and_event_class_id"
    t.index ["event_id"], name: "index_gcalendar_events_on_event_id", unique: true
    t.index ["starts_at"], name: "index_gcalendar_events_on_starts_at"
  end

  create_table "groups", force: :cascade do |t|
    t.string "name"
    t.string "group_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "config", default: {}, null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_groups_on_discarded_at"
  end

  create_table "hiring_pipeline_column_filters", force: :cascade do |t|
    t.bigint "hiring_pipeline_column_setting_id", null: false
    t.integer "order_level", default: 0, null: false
    t.boolean "inclusion", default: true, null: false
    t.string "applied_values", default: [], null: false, array: true
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_hiring_pipeline_column_filters_on_discarded_at"
    t.index ["hiring_pipeline_column_setting_id"], name: "index_hiring_pipeline_column_filters_on_setting_id", unique: true
  end

  create_table "hiring_pipeline_column_settings", force: :cascade do |t|
    t.bigint "job_vacancy_id"
    t.string "key"
    t.boolean "value", default: false
    t.integer "order_level"
    t.string "state"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "label"
    t.string "group"
    t.boolean "default", default: false
    t.jsonb "config", default: {}, null: false
    t.boolean "filterable", default: true, null: false
    t.index ["discarded_at"], name: "index_hiring_pipeline_column_settings_on_discarded_at"
    t.index ["job_vacancy_id", "key", "state"], name: "index_pipeline_key_column_settings_unique", unique: true
    t.index ["job_vacancy_id", "state"], name: "index_pipeline_column_settings_on_job_vacancy_id_and_state"
    t.index ["job_vacancy_id"], name: "index_hiring_pipeline_column_settings_on_job_vacancy_id"
    t.index ["state"], name: "index_hiring_pipeline_column_settings_on_state"
  end

  create_table "hiring_pipelines", force: :cascade do |t|
    t.bigint "job_vacancy_id"
    t.string "name"
    t.integer "order_level"
    t.jsonb "details", default: {}
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "metadata", default: {}, null: false
    t.index ["discarded_at"], name: "index_hiring_pipelines_on_discarded_at"
    t.index ["job_vacancy_id", "name"], name: "index_hiring_pipelines_on_job_vacancy_id_and_name", unique: true
    t.index ["job_vacancy_id"], name: "index_hiring_pipelines_on_job_vacancy_id"
  end

  create_table "hiring_processes", force: :cascade do |t|
    t.string "stage"
    t.enum "work_type", enum_type: "work_type"
    t.string "hiring_state"
    t.text "message"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.bigint "user_job_vacancy_id"
    t.string "interviewer_name"
    t.string "interviewer_position"
    t.string "additional_url"
    t.string "work_period"
    t.float "salary"
    t.jsonb "documents", default: [], null: false
    t.text "reason"
    t.string "location"
    t.text "preparation"
    t.string "interviewer_email"
    t.string "company_contact_email"
    t.string "company_contact_phone"
    t.string "company_contact_name"
    t.string "company_contact_position"
    t.index ["discarded_at"], name: "index_hiring_processes_on_discarded_at"
    t.index ["user_id"], name: "index_hiring_processes_on_user_id"
    t.index ["user_job_vacancy_id"], name: "index_hiring_processes_on_user_job_vacancy_id"
  end

  create_table "homework_options", force: :cascade do |t|
    t.string "option", null: false
    t.enum "option_type", enum_type: "option_type"
    t.boolean "correct_option"
    t.bigint "homework_question_id"
    t.datetime "discarded_at", precision: nil
    t.integer "order_level", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "least_aspect"
    t.string "most_aspect"
    t.jsonb "config", default: {}, null: false
    t.index ["discarded_at"], name: "index_homework_options_on_discarded_at"
    t.index ["homework_question_id", "order_level"], name: "index_homework_options_on_homework_question_id_and_order_level", unique: true
    t.index ["homework_question_id"], name: "index_homework_options_on_homework_question_id"
  end

  create_table "homework_question_taggings", force: :cascade do |t|
    t.integer "order_level", default: 0, null: false
    t.bigint "homework_question_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "tagging_id"
    t.index ["discarded_at"], name: "index_homework_question_taggings_on_discarded_at"
    t.index ["homework_question_id", "tagging_id"], name: "unique_index_questions_and_taggings", unique: true
    t.index ["homework_question_id"], name: "index_homework_question_taggings_on_homework_question_id"
    t.index ["tagging_id"], name: "index_homework_question_taggings_on_tagging_id"
  end

  create_table "homework_questions", force: :cascade do |t|
    t.string "title", null: false
    t.text "desc", null: false
    t.string "homework_type"
    t.integer "order_level"
    t.bigint "module_session_id"
    t.integer "import_source_id"
    t.datetime "discarded_at", precision: nil
    t.enum "difficulty", default: "beginner", enum_type: "difficulty"
    t.string "answer_keys", array: true
    t.float "default_score", default: 0.0
    t.string "instruction_document_url"
    t.bigint "bundling_code_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "bonus_question", default: false, null: false
    t.bigint "assessment_item_id"
    t.bigint "stimulus_id"
    t.integer "duration", default: 0
    t.string "aspect"
    t.integer "scale", default: 0
    t.string "negative_and_low_scale_desc", default: ""
    t.string "positive_and_high_scale_desc", default: ""
    t.jsonb "config", default: {}
    t.string "dimension"
    t.index ["assessment_item_id"], name: "index_homework_questions_on_assessment_item_id"
    t.index ["bundling_code_id"], name: "index_homework_questions_on_bundling_code_id"
    t.index ["difficulty"], name: "index_homework_questions_on_difficulty"
    t.index ["discarded_at"], name: "index_homework_questions_on_discarded_at"
    t.index ["module_session_id", "bundling_code_id", "order_level"], name: "index_homework_questions_on_ms_id_bc_id_and_order_level", unique: true
    t.index ["module_session_id"], name: "index_homework_questions_on_module_session_id"
    t.index ["stimulus_id"], name: "index_homework_questions_on_stimulus_id"
  end

  create_table "industry_categories", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "verified", default: true, null: false
    t.datetime "discarded_at", precision: nil
    t.index ["name"], name: "index_industry_categories_on_name", unique: true
  end

  create_table "installment_requests", force: :cascade do |t|
    t.enum "status", default: "waiting_for_approval", enum_type: "installment_request_status"
    t.text "action_note"
    t.boolean "email_sent", default: false, null: false
    t.string "signed_contract_letter_url"
    t.bigint "user_id"
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_installment_requests_on_discarded_at"
    t.index ["user_id"], name: "index_installment_requests_on_user_id"
  end

  create_table "invite_assesses", force: :cascade do |t|
    t.string "status"
    t.string "uploaded_file_url"
    t.string "result_file_url"
    t.bigint "admin_id"
    t.boolean "use_token"
    t.datetime "token_expired_at"
    t.datetime "starts_at"
    t.boolean "use_external_assessment"
    t.boolean "with_login"
    t.integer "total_rows"
    t.bigint "mailer_template_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_id"], name: "index_invite_assesses_on_admin_id"
    t.index ["discarded_at"], name: "index_invite_assesses_on_discarded_at"
    t.index ["mailer_template_id"], name: "index_invite_assesses_on_mailer_template_id"
  end

  create_table "invoice_numbers", force: :cascade do |t|
    t.integer "counter", default: 1, null: false
  end

  create_table "invoice_promo_codes", force: :cascade do |t|
    t.bigint "promo_code_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "invoice_type"
    t.bigint "invoice_id"
    t.index ["discarded_at"], name: "index_invoice_promo_codes_on_discarded_at"
    t.index ["invoice_id", "invoice_type", "promo_code_id"], name: "index_invoice_promo_codes_on_invoice_and_promo_code", unique: true
    t.index ["invoice_type", "invoice_id"], name: "index_invoice_promo_codes_on_invoice_type_and_invoice_id"
    t.index ["promo_code_id"], name: "index_invoice_promo_codes_on_promo_code_id"
  end

  create_table "job_group_job_roles", force: :cascade do |t|
    t.bigint "job_role_group_id"
    t.bigint "job_role_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_job_group_job_roles_on_discarded_at"
    t.index ["job_role_group_id"], name: "index_job_group_job_roles_on_job_role_group_id"
    t.index ["job_role_id"], name: "index_job_group_job_roles_on_job_role_id"
  end

  create_table "job_guarantees", force: :cascade do |t|
    t.string "benefits", array: true
    t.float "price", default: 0.0, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.string "external_id"
    t.integer "rakamin_point", default: 0, null: false
    t.string "program"
    t.jsonb "features", default: []
    t.jsonb "schedules", default: []
    t.string "timeline_url"
    t.string "policy_url"
    t.string "zoom_url"
    t.string "intro_url"
    t.index ["discarded_at"], name: "index_job_guarantees_on_discarded_at"
    t.index ["external_id"], name: "index_job_guarantees_on_external_id", unique: true
  end

  create_table "job_info_competencies", force: :cascade do |t|
    t.bigint "competency_id"
    t.bigint "job_info_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["competency_id"], name: "index_job_info_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_job_info_competencies_on_discarded_at"
    t.index ["job_info_id", "competency_id"], name: "index_job_info_competencies_on_job_info_id_and_competency_id", unique: true
    t.index ["job_info_id"], name: "index_job_info_competencies_on_job_info_id"
  end

  create_table "job_info_industry_categories", force: :cascade do |t|
    t.bigint "industry_category_id"
    t.bigint "job_info_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "job_info_industry_category_type", default: "expertise"
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_job_info_industry_categories_on_discarded_at"
    t.index ["industry_category_id"], name: "index_job_info_industry_categories_on_industry_category_id"
    t.index ["job_info_id", "industry_category_id"], name: "job_info_industry_categories_unique_index", unique: true
    t.index ["job_info_id"], name: "index_job_info_industry_categories_on_job_info_id"
    t.index ["job_info_industry_category_type"], name: "job_info_industry_categories_type_index"
  end

  create_table "job_infos", force: :cascade do |t|
    t.integer "salary_starts_at", default: 0
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "latest_salary_starts_at", default: 0
    t.bigint "latest_salary_ends_at", default: 0
    t.bigint "expected_salary", default: 0
    t.text "note"
    t.bigint "expected_salary_starts_at", default: 0
    t.bigint "expected_salary_ends_at", default: 0
    t.float "weight"
    t.float "height"
    t.index ["user_id"], name: "index_job_infos_on_user_id", unique: true
  end

  create_table "job_levels", force: :cascade do |t|
    t.string "name"
    t.bigint "partner_id", null: false
    t.integer "order_level", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_job_levels_on_discarded_at"
    t.index ["partner_id"], name: "index_job_levels_on_partner_id"
  end

  create_table "job_offers", force: :cascade do |t|
    t.bigint "job_vacancy_id"
    t.bigint "user_job_vacancy_id"
    t.float "salary", default: 0.0
    t.text "benefit"
    t.datetime "contract_starts_at", precision: nil
    t.datetime "contract_ends_at", precision: nil
    t.string "offering_letter_url"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_job_offers_on_discarded_at"
    t.index ["job_vacancy_id", "user_job_vacancy_id"], name: "index_job_offers_on_job_vacancy_id_and_user_job_vacancy_id", unique: true
    t.index ["job_vacancy_id"], name: "index_job_offers_on_job_vacancy_id"
    t.index ["user_job_vacancy_id"], name: "index_job_offers_on_user_job_vacancy_id"
  end

  create_table "job_requirements", force: :cascade do |t|
    t.float "current_salary"
    t.float "expected_salary"
    t.boolean "negotiable"
    t.string "job_type"
    t.string "expected_location"
    t.string "expected_level"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_job_requirements_on_user_id"
  end

  create_table "job_role_groups", force: :cascade do |t|
    t.string "name"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "valid_filter", default: true, null: false
    t.index ["discarded_at"], name: "index_job_role_groups_on_discarded_at"
  end

  create_table "job_roles", force: :cascade do |t|
    t.string "name"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "verified", default: false
    t.jsonb "partner_metadata", default: {}, null: false
    t.index ["discarded_at"], name: "index_job_roles_on_discarded_at"
    t.index ["name"], name: "index_job_roles_on_name", unique: true
  end

  create_table "job_vacancies", force: :cascade do |t|
    t.bigint "partner_id"
    t.string "name"
    t.string "job_type"
    t.bigint "job_role_id"
    t.float "minimum_salary", default: 0.0
    t.float "maximum_salary", default: 0.0
    t.text "description"
    t.string "state"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "job_vacancy_type", default: "talent_scouting", null: false
    t.integer "quota"
    t.text "qualifications", default: ""
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.bigint "job_role_group_id"
    t.string "job_level"
    t.bigint "education_level_id"
    t.bigint "location_id"
    t.integer "min_age"
    t.integer "max_age"
    t.bigint "industry_category_id"
    t.jsonb "config", default: {}
    t.integer "match_progress_percentage", default: 0
    t.string "work_mode"
    t.string "custom_tool_names", default: [], null: false, array: true
    t.string "custom_competency_names", default: [], null: false, array: true
    t.string "shared_channels", default: [], null: false, array: true
    t.boolean "show_salary", default: true
    t.string "external_id"
    t.jsonb "ujv_counts", default: {}, null: false
    t.jsonb "hiring_times", default: {}, null: false
    t.datetime "registration_starts_at", precision: nil
    t.datetime "registration_ends_at", precision: nil
    t.integer "public_location_id"
    t.string "job_grade"
    t.string "department_name"
    t.bigint "partner_placement_id"
    t.string "visibility", default: "public"
    t.index ["discarded_at"], name: "index_job_vacancies_on_discarded_at"
    t.index ["education_level_id"], name: "index_job_vacancies_on_education_level_id"
    t.index ["external_id"], name: "index_job_vacancies_on_external_id", unique: true
    t.index ["industry_category_id"], name: "index_job_vacancies_on_industry_category_id"
    t.index ["job_role_group_id"], name: "index_job_vacancies_on_job_role_group_id"
    t.index ["job_role_id"], name: "index_job_vacancies_on_job_role_id"
    t.index ["job_vacancy_type"], name: "index_job_vacancies_on_job_vacancy_type"
    t.index ["location_id"], name: "index_job_vacancies_on_location_id"
    t.index ["partner_id"], name: "index_job_vacancies_on_partner_id"
    t.index ["partner_placement_id"], name: "index_job_vacancies_on_partner_placement_id"
    t.index ["public_location_id"], name: "index_job_vacancies_on_public_location_id"
    t.index ["visibility"], name: "index_job_vacancies_on_visibility"
  end

  create_table "job_vacancy_assessments", force: :cascade do |t|
    t.bigint "job_vacancy_id"
    t.bigint "company_assessment_template_id"
    t.string "status", default: "active", null: false
    t.string "assessment_period", default: "after_applying", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "eligible_duration", default: 1, null: false
    t.jsonb "config", default: {}, null: false
    t.jsonb "talent_statistics", default: {}, null: false
    t.index ["company_assessment_template_id"], name: "index_job_vacancy_assessments_on_company_assessment_template_id"
    t.index ["discarded_at"], name: "index_job_vacancy_assessments_on_discarded_at"
    t.index ["job_vacancy_id", "company_assessment_template_id"], name: "index_unique_job_vacancy_assessment_template", unique: true
    t.index ["job_vacancy_id"], name: "index_job_vacancy_assessments_on_job_vacancy_id"
  end

  create_table "job_vacancy_competencies", force: :cascade do |t|
    t.bigint "job_vacancy_id", null: false
    t.bigint "competency_id", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "weight", default: "low"
    t.index ["competency_id", "job_vacancy_id"], name: "index_job_vacancy_competencies_on_comp_id_and_job_vacancy_id", unique: true
    t.index ["competency_id"], name: "index_job_vacancy_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_job_vacancy_competencies_on_discarded_at"
    t.index ["job_vacancy_id"], name: "index_job_vacancy_competencies_on_job_vacancy_id"
  end

  create_table "job_vacancy_licenses", force: :cascade do |t|
    t.bigint "job_vacancy_id", null: false
    t.bigint "license_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_job_vacancy_licenses_on_discarded_at"
    t.index ["job_vacancy_id", "license_id"], name: "index_job_vacancy_licenses_on_job_vacancy_id_and_license_id", unique: true
    t.index ["job_vacancy_id"], name: "index_job_vacancy_licenses_on_job_vacancy_id"
    t.index ["license_id"], name: "index_job_vacancy_licenses_on_license_id"
  end

  create_table "job_vacancy_recruiters", force: :cascade do |t|
    t.bigint "job_vacancy_id", null: false
    t.bigint "recruiter_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_job_vacancy_recruiters_on_discarded_at"
    t.index ["job_vacancy_id", "recruiter_id"], name: "index_job_vacancy_recruiters_on_job_vacancy_id_and_recruiter_id", unique: true
    t.index ["job_vacancy_id"], name: "index_job_vacancy_recruiters_on_job_vacancy_id"
    t.index ["recruiter_id"], name: "index_job_vacancy_recruiters_on_recruiter_id"
  end

  create_table "job_vacancy_section_questions", force: :cascade do |t|
    t.bigint "job_vacancy_section_id", null: false
    t.bigint "vacancy_question_id", null: false
    t.integer "order_level", default: 0, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_job_vacancy_section_questions_on_discarded_at"
    t.index ["job_vacancy_section_id", "vacancy_question_id"], name: "index_job_vacancy_section_questions_on_section_and_question", unique: true
    t.index ["job_vacancy_section_id"], name: "index_job_vacancy_section_questions_on_job_vacancy_section_id"
    t.index ["vacancy_question_id"], name: "index_job_vacancy_section_questions_on_vacancy_question_id"
  end

  create_table "job_vacancy_sections", force: :cascade do |t|
    t.bigint "job_vacancy_id", null: false
    t.string "title", null: false
    t.integer "order_level", default: 0, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_job_vacancy_sections_on_discarded_at"
    t.index ["job_vacancy_id"], name: "index_job_vacancy_sections_on_job_vacancy_id"
  end

  create_table "job_vacancy_taggings", force: :cascade do |t|
    t.bigint "job_vacancy_id", null: false
    t.string "name"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_job_vacancy_taggings_on_discarded_at"
    t.index ["job_vacancy_id", "name"], name: "index_job_vacancy_taggings_on_job_vacancy_id_and_name", unique: true
    t.index ["job_vacancy_id"], name: "index_job_vacancy_taggings_on_job_vacancy_id"
  end

  create_table "job_vacancy_university_majors", force: :cascade do |t|
    t.bigint "job_vacancy_id"
    t.bigint "university_major_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.integer "pub_univ_major_id"
    t.index ["discarded_at"], name: "index_job_vacancy_university_majors_on_discarded_at"
    t.index ["job_vacancy_id", "university_major_id"], name: "index_job_vacancy_university_majors_on_jv_id_and_um_id", unique: true
    t.index ["job_vacancy_id"], name: "index_job_vacancy_university_majors_on_job_vacancy_id"
    t.index ["pub_univ_major_id"], name: "index_job_vacancy_university_majors_on_pub_univ_major_id"
    t.index ["university_major_id"], name: "index_job_vacancy_university_majors_on_university_major_id"
  end

  create_table "leadership_experiences", force: :cascade do |t|
    t.string "title", null: false
    t.string "organization"
    t.datetime "starts_at"
    t.datetime "ends_at"
    t.text "description"
    t.bigint "user_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_leadership_experiences_on_discarded_at"
    t.index ["user_id"], name: "index_leadership_experiences_on_user_id"
  end

  create_table "licenses", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_licenses_on_discarded_at"
  end

  create_table "linked_contacts", force: :cascade do |t|
    t.string "name"
    t.string "address"
    t.string "relation"
    t.string "phone"
    t.bigint "user_id"
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_linked_contacts_on_discarded_at"
    t.index ["user_id"], name: "index_linked_contacts_on_user_id"
  end

  create_table "locales", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "language_code"
    t.jsonb "properties", default: {}
    t.index ["language_code"], name: "index_locales_on_language_code", unique: true
  end

  create_table "locations", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.boolean "verified", default: true, null: false
    t.index ["discarded_at"], name: "index_locations_on_discarded_at"
    t.index ["name"], name: "index_locations_on_name", unique: true
  end

  create_table "mailer_templates", force: :cascade do |t|
    t.string "pipeline_type"
    t.string "custom_email_name"
    t.bigint "latest_update_admin_id"
    t.string "subject_email"
    t.string "company_logo_url"
    t.string "title_email"
    t.string "body_email"
    t.string "email_template"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "partner_id"
    t.string "footer_logo_url"
    t.string "footer_name"
    t.string "footer_address"
    t.string "footer_linkedin"
    t.string "footer_instagram"
    t.string "footer_whatsapp"
    t.bigint "qiscus_message_template_id"
    t.jsonb "config", default: {}, null: false
    t.index ["discarded_at"], name: "index_mailer_templates_on_discarded_at"
    t.index ["latest_update_admin_id"], name: "index_mailer_templates_on_latest_update_admin_id"
    t.index ["partner_id"], name: "index_mailer_templates_on_partner_id"
    t.index ["qiscus_message_template_id"], name: "index_mailer_templates_on_qiscus_message_template_id"
  end

  create_table "main_characters", force: :cascade do |t|
    t.string "name"
    t.string "external_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_main_characters_on_discarded_at"
    t.index ["external_id"], name: "index_main_characters_on_external_id", unique: true
  end

  create_table "majors", force: :cascade do |t|
    t.string "name"
    t.string "code"
    t.string "director_name"
    t.string "manager_name"
    t.string "director_signature_url"
    t.string "manager_signature_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "pic_role", default: "Learning Manager", null: false
    t.index ["code"], name: "index_majors_on_code", unique: true
  end

  create_table "mandatory_documents", force: :cascade do |t|
    t.string "scanned_ktp_url"
    t.string "scanned_kk_url"
    t.string "selfie_ktp_url"
    t.string "salary_slip_url"
    t.bigint "user_id"
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_mandatory_documents_on_discarded_at"
    t.index ["user_id"], name: "index_mandatory_documents_on_user_id"
  end

  create_table "mapping_professions", force: :cascade do |t|
    t.bigint "profession_id"
    t.integer "score", default: 0
    t.string "external_id"
    t.bigint "assessment_persona_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["assessment_persona_id"], name: "index_mapping_professions_on_assessment_persona_id"
    t.index ["discarded_at"], name: "index_mapping_professions_on_discarded_at"
    t.index ["external_id"], name: "index_mapping_professions_on_external_id", unique: true
    t.index ["profession_id", "assessment_persona_id"], name: "index_mapping_professions", unique: true
    t.index ["profession_id"], name: "index_mapping_professions_on_profession_id"
  end

  create_table "matchmaking_queues", force: :cascade do |t|
    t.bigint "job_vacancy_id", null: false
    t.string "status"
    t.jsonb "config", default: {}, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "raw_response"
    t.index ["discarded_at"], name: "index_matchmaking_queues_on_discarded_at"
    t.index ["job_vacancy_id"], name: "index_matchmaking_queues_on_job_vacancy_id"
    t.index ["status"], name: "index_matchmaking_queues_on_status"
  end

  create_table "mentoring_groups", force: :cascade do |t|
    t.bigint "course_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "assistant_tutor_id"
    t.datetime "discarded_at", precision: nil
    t.index ["assistant_tutor_id"], name: "index_mentoring_groups_on_assistant_tutor_id"
    t.index ["course_id"], name: "index_mentoring_groups_on_course_id"
    t.index ["discarded_at"], name: "index_mentoring_groups_on_discarded_at"
  end

  create_table "module_sessions", force: :cascade do |t|
    t.string "name"
    t.integer "order_level"
    t.enum "session_type", enum_type: "session_type"
    t.bigint "course_id"
    t.bigint "course_module_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "session_url"
    t.string "video_url"
    t.text "description"
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.datetime "due_date", precision: nil
    t.string "video_duration"
    t.datetime "session_date", precision: nil
    t.integer "tutor_id"
    t.datetime "discarded_at", precision: nil
    t.string "reference_urls", array: true
    t.integer "total_questions", default: 0
    t.jsonb "question_composition", default: {"advance"=>0, "beginner"=>0, "intermediate"=>0}
    t.string "state", default: "draft"
    t.jsonb "config", default: {}
    t.string "answer_guidance_url"
    t.string "progress_state", default: "upcoming"
    t.bigint "vwx_task_component_id"
    t.integer "reward_xp", default: 0
    t.string "literature_urls", default: [], array: true
    t.integer "duration"
    t.datetime "last_published_at", precision: nil
    t.float "min_completion_score"
    t.integer "remedial_session_id"
    t.string "instruction"
    t.integer "max_retry"
    t.integer "extra_time_duration", default: 0
    t.index ["course_id"], name: "index_module_sessions_on_course_id"
    t.index ["course_module_id"], name: "index_module_sessions_on_course_module_id"
    t.index ["discarded_at"], name: "index_module_sessions_on_discarded_at"
    t.index ["progress_state"], name: "index_module_sessions_on_progress_state"
    t.index ["starts_at"], name: "index_module_sessions_on_starts_at"
    t.index ["state"], name: "index_module_sessions_on_state", using: :hash
    t.index ["vwx_task_component_id"], name: "index_module_sessions_on_vwx_task_component_id"
  end

  create_table "notifications", force: :cascade do |t|
    t.string "phase"
    t.string "talent_csv_url"
    t.datetime "announced_at", precision: nil
    t.string "email_for"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_notifications_on_discarded_at"
  end

  create_table "organization_experiences", force: :cascade do |t|
    t.string "role"
    t.string "organization_name"
    t.string "organization_level"
    t.string "organization_type"
    t.string "team_size"
    t.datetime "start_date"
    t.datetime "end_date"
    t.text "description"
    t.bigint "user_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_organization_experiences_on_discarded_at"
    t.index ["user_id"], name: "index_organization_experiences_on_user_id"
  end

  create_table "organizations", force: :cascade do |t|
    t.string "identifier", null: false
    t.string "scheme", null: false
    t.string "host", null: false
    t.string "name"
    t.string "logo_url"
    t.datetime "discarded_at", precision: nil
    t.jsonb "color"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "favicon"
    t.string "favicon_32"
    t.string "favicon_16"
    t.string "safari_pin"
    t.string "web_title"
    t.string "web_description"
    t.string "apple_touch"
    t.string "theme_color"
    t.string "webmanifest"
    t.jsonb "config", default: {}
    t.jsonb "internal_config", default: {}, null: false
    t.string "alias_hosts", default: [], null: false, array: true
    t.index ["alias_hosts"], name: "index_organizations_on_alias_hosts", using: :gin
    t.index ["discarded_at"], name: "index_organizations_on_discarded_at"
    t.index ["host"], name: "index_organizations_on_host", unique: true
    t.index ["identifier", "scheme", "host"], name: "index_organizations_on_identifier_and_scheme_and_host", unique: true
  end

  create_table "partner_competencies", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.string "name"
    t.text "description"
    t.string "core_value"
    t.float "eligible_target"
    t.integer "level"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_partner_competencies_on_discarded_at"
    t.index ["partner_id"], name: "index_partner_competencies_on_partner_id"
  end

  create_table "partner_attributes", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.string "key", null: false
    t.string "label"
    t.string "data_type"
    t.jsonb "config", default: {}, null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_partner_attributes_on_discarded_at"
    t.index ["partner_id", "key"], name: "index_partner_attributes_on_partner_id_and_key", unique: true
    t.index ["partner_id"], name: "index_partner_attributes_on_partner_id"
  end

  create_table "partner_placements", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.string "name", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_partner_placements_on_discarded_at"
    t.index ["partner_id", "name"], name: "index_partner_placements_on_partner_id_and_name", unique: true
    t.index ["partner_id"], name: "index_partner_placements_on_partner_id"
  end

  create_table "partner_saved_talents", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "partner_id", null: false
    t.integer "job_vacancy_ids", default: [], null: false, array: true
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_partner_saved_talents_on_discarded_at"
    t.index ["partner_id"], name: "index_partner_saved_talents_on_partner_id"
    t.index ["updated_at"], name: "index_partner_saved_talents_on_updated_at"
    t.index ["user_id", "partner_id"], name: "index_partner_saved_talents_on_user_id_and_partner_id", unique: true
    t.index ["user_id"], name: "index_partner_saved_talents_on_user_id"
  end

  create_table "partner_teams", force: :cascade do |t|
    t.string "email"
    t.string "invite_status", default: "pending"
    t.string "invite_token"
    t.datetime "discarded_at", precision: nil
    t.bigint "partner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_partner_teams_on_discarded_at"
    t.index ["partner_id", "email"], name: "index_partner_teams_on_partner_id_and_email", unique: true
    t.index ["partner_id"], name: "index_partner_teams_on_partner_id"
  end

  create_table "partners", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "about_us"
    t.string "logo_url"
    t.string "bg_image_url"
    t.string "bg_color"
    t.string "signature_of"
    t.string "signature_of_url"
    t.string "signature_of_role"
    t.string "certificate_logo_url"
    t.string "access_tier", default: "free", null: false
    t.datetime "access_tier_ends_at", precision: nil
    t.string "eligible_vacancy_types", default: ["talent_scouting"], null: false, array: true
    t.integer "premium_talent_limit", default: 0, null: false
    t.integer "premium_talent_ids", default: [], null: false, array: true
    t.jsonb "config", default: {}, null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_partners_on_discarded_at"
  end

  create_table "permission_groups", force: :cascade do |t|
    t.string "key"
    t.string "value"
    t.bigint "group_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["group_id", "key"], name: "index_permission_groups_on_group_id_and_key", unique: true
    t.index ["group_id"], name: "index_permission_groups_on_group_id"
  end

  create_table "pg_search_documents", force: :cascade do |t|
    t.text "content"
    t.string "searchable_type"
    t.bigint "searchable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["content"], name: "index_pg_search_documents_on_content", opclass: :gin_trgm_ops, using: :gin
    t.index ["searchable_type", "searchable_id"], name: "index_pg_search_documents_on_searchable_type_and_searchable_id"
  end

  create_table "portfolio_competencies", force: :cascade do |t|
    t.bigint "portfolio_id"
    t.bigint "competency_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competency_id"], name: "index_portfolio_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_portfolio_competencies_on_discarded_at"
    t.index ["portfolio_id", "competency_id"], name: "index_unique_portfolio_id_competency_id", unique: true
    t.index ["portfolio_id"], name: "index_portfolio_competencies_on_portfolio_id"
  end

  create_table "portfolios", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "project_url"
    t.boolean "public_access_status", default: true
    t.datetime "discarded_at", precision: nil
    t.bigint "vwx_id"
    t.index ["discarded_at"], name: "index_portfolios_on_discarded_at"
    t.index ["user_id"], name: "index_portfolios_on_user_id"
    t.index ["vwx_id"], name: "index_portfolios_on_vwx_id"
  end

  create_table "prakerja_statuses", force: :cascade do |t|
    t.string "state"
    t.boolean "attendance_status", default: false
    t.integer "sequence", default: 0
    t.jsonb "response_data", default: []
    t.bigint "promo_code_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "course_id"
    t.bigint "user_id"
    t.string "status", default: "enqueued"
    t.string "oauth_url"
    t.index ["course_id"], name: "index_prakerja_statuses_on_course_id"
    t.index ["discarded_at"], name: "index_prakerja_statuses_on_discarded_at"
    t.index ["promo_code_id"], name: "index_prakerja_statuses_on_promo_code_id"
    t.index ["state"], name: "index_prakerja_statuses_on_state", unique: true
    t.index ["user_id"], name: "index_prakerja_statuses_on_user_id"
  end

  create_table "profession_categories", force: :cascade do |t|
    t.string "name"
    t.string "external_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_profession_categories_on_discarded_at"
    t.index ["external_id"], name: "index_profession_categories_on_external_id", unique: true
  end

  create_table "professions", force: :cascade do |t|
    t.string "external_id"
    t.string "name"
    t.bigint "profession_category_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.string "link"
    t.index ["discarded_at"], name: "index_professions_on_discarded_at"
    t.index ["external_id"], name: "index_professions_on_external_id", unique: true
    t.index ["profession_category_id"], name: "index_professions_on_profession_category_id"
  end

  create_table "profile_persona_aspects", force: :cascade do |t|
    t.string "external_id"
    t.bigint "profile_persona_id"
    t.bigint "assessment_persona_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["assessment_persona_id", "profile_persona_id"], name: "index_profile_persona_aspects", unique: true
    t.index ["assessment_persona_id"], name: "index_profile_persona_aspects_on_assessment_persona_id"
    t.index ["discarded_at"], name: "index_profile_persona_aspects_on_discarded_at"
    t.index ["external_id"], name: "index_profile_persona_aspects_on_external_id", unique: true
    t.index ["profile_persona_id"], name: "index_profile_persona_aspects_on_profile_persona_id"
  end

  create_table "profile_persona_characters", force: :cascade do |t|
    t.string "external_id"
    t.bigint "main_character_id"
    t.bigint "profile_persona_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_profile_persona_characters_on_discarded_at"
    t.index ["external_id"], name: "index_profile_persona_characters_on_external_id", unique: true
    t.index ["main_character_id", "profile_persona_id"], name: "index_profile_persona_characters", unique: true
    t.index ["main_character_id"], name: "index_profile_persona_characters_on_main_character_id"
    t.index ["profile_persona_id"], name: "index_profile_persona_characters_on_profile_persona_id"
  end

  create_table "profile_personas", force: :cascade do |t|
    t.string "name"
    t.string "icon_url"
    t.string "external_id"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_profile_personas_on_discarded_at"
    t.index ["external_id"], name: "index_profile_personas_on_external_id", unique: true
  end

  create_table "program_registrations", force: :cascade do |t|
    t.string "email", null: false
    t.string "fullname"
    t.string "nickname"
    t.string "gender"
    t.string "address"
    t.string "phone_number"
    t.string "postal_code"
    t.string "info_source"
    t.string "student_card_url"
    t.string "cv_url"
    t.string "grade_score_evidence_url"
    t.string "graduation_evidence_url"
    t.datetime "date_of_birth", precision: nil
    t.datetime "expected_graduation", precision: nil
    t.float "grade_score"
    t.string "semester"
    t.boolean "has_nuclear_family"
    t.boolean "agreement_approval"
    t.bigint "province_id"
    t.bigint "location_id"
    t.bigint "university_id"
    t.bigint "education_level_id"
    t.bigint "university_major_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.integer "public_location_id"
    t.integer "pub_univ_major_id"
    t.index ["discarded_at"], name: "index_program_registrations_on_discarded_at"
    t.index ["education_level_id"], name: "index_program_registrations_on_education_level_id"
    t.index ["location_id"], name: "index_program_registrations_on_location_id"
    t.index ["province_id"], name: "index_program_registrations_on_province_id"
    t.index ["pub_univ_major_id"], name: "index_program_registrations_on_pub_univ_major_id"
    t.index ["public_location_id"], name: "index_program_registrations_on_public_location_id"
    t.index ["university_id"], name: "index_program_registrations_on_university_id"
    t.index ["university_major_id"], name: "index_program_registrations_on_university_major_id"
  end

  create_table "project_submissions", force: :cascade do |t|
    t.bigint "project_id"
    t.bigint "module_session_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["module_session_id"], name: "index_project_submissions_on_module_session_id"
    t.index ["project_id", "module_session_id"], name: "index_project_submissions_on_project_id_and_module_session_id", unique: true
    t.index ["project_id"], name: "index_project_submissions_on_project_id"
  end

  create_table "projects", force: :cascade do |t|
    t.string "title"
    t.string "description"
    t.string "topic"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "promo_codes", force: :cascade do |t|
    t.string "code", null: false
    t.float "discount_price", default: 0.0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.enum "promo_type", default: "promo", null: false, enum_type: "promo_type"
    t.datetime "expired_at", precision: nil
    t.bigint "user_id"
    t.string "title"
    t.string "description"
    t.text "terms_and_conditions", array: true
    t.enum "applied_type", enum_type: "applied_type"
    t.bigint "job_guarantee_id"
    t.string "payment_options", default: ["full_payment", "3_months_installment", "6_months_installment"], array: true
    t.string "visibility", default: "public"
    t.integer "max_usage"
    t.string "eligible_emails", default: [], null: false, array: true
    t.string "external_resource"
    t.string "external_code"
    t.datetime "claimed_at", precision: nil
    t.bigint "course_id"
    t.index ["code"], name: "index_promo_codes_on_code", unique: true
    t.index ["course_id"], name: "index_promo_codes_on_course_id"
    t.index ["discarded_at"], name: "index_promo_codes_on_discarded_at"
    t.index ["job_guarantee_id"], name: "index_promo_codes_on_job_guarantee_id"
    t.index ["user_id", "promo_type"], name: "index_promo_codes_on_user_id_and_promo_type"
    t.index ["user_id"], name: "index_promo_codes_on_user_id"
    t.index ["visibility"], name: "index_promo_codes_on_visibility"
  end

  create_table "provinces", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_provinces_on_discarded_at"
  end

  create_table "qiscus_message_templates", force: :cascade do |t|
    t.string "namespace"
    t.string "template_name", null: false
    t.string "language_code"
    t.jsonb "variable_mappings", default: {}, null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_qiscus_message_templates_on_discarded_at"
    t.index ["template_name"], name: "index_qiscus_message_templates_on_template_name", unique: true
  end

  create_table "qiscus_messages", force: :cascade do |t|
    t.bigint "qiscus_message_template_id", null: false
    t.bigint "user_id", null: false
    t.string "message_id", null: false
    t.string "target_number"
    t.string "status"
    t.jsonb "raw_callbacks", default: [], null: false, array: true
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_qiscus_messages_on_discarded_at"
    t.index ["message_id"], name: "index_qiscus_messages_on_message_id", unique: true
    t.index ["qiscus_message_template_id"], name: "index_qiscus_messages_on_qiscus_message_template_id"
    t.index ["user_id"], name: "index_qiscus_messages_on_user_id"
  end

  create_table "redeemed_promo_codes", force: :cascade do |t|
    t.bigint "promo_code_id"
    t.bigint "user_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_redeemed_promo_codes_on_discarded_at"
    t.index ["promo_code_id", "user_id"], name: "index_redeemed_promo_codes_on_promo_code_id_and_user_id", unique: true
    t.index ["promo_code_id"], name: "index_redeemed_promo_codes_on_promo_code_id"
    t.index ["user_id"], name: "index_redeemed_promo_codes_on_user_id"
  end

  create_table "report_assessments", force: :cascade do |t|
    t.bigint "user_job_vacancy_assessment_id", null: false
    t.string "assessment_type", null: false
    t.string "pdf_link", null: false
    t.json "score", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_report_assessments_on_discarded_at"
    t.index ["user_job_vacancy_assessment_id"], name: "index_report_assessments_on_user_job_vacancy_assessment_id", unique: true
  end

  create_table "rewards", force: :cascade do |t|
    t.string "image_url"
    t.string "name"
    t.string "reward_type"
    t.text "detail"
    t.text "how_to_use"
    t.datetime "expired_date", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "promo_code_id"
    t.index ["discarded_at"], name: "index_rewards_on_discarded_at"
    t.index ["promo_code_id"], name: "index_rewards_on_promo_code_id"
    t.index ["reward_type"], name: "index_rewards_on_reward_type"
  end

  create_table "scoring_summaries", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "mentoring_group_id"
    t.jsonb "scoring_summaries", default: []
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "final_score", default: 0.0
    t.index ["mentoring_group_id"], name: "index_scoring_summaries_on_mentoring_group_id"
    t.index ["user_id"], name: "index_scoring_summaries_on_user_id"
  end

  create_table "scoring_templates", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_scoring_templates_on_discarded_at"
  end

  create_table "shared_vacancy_tokens", force: :cascade do |t|
    t.string "token"
    t.bigint "job_vacancy_id"
    t.string "state"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "counter", default: 0
    t.index ["discarded_at"], name: "index_shared_vacancy_tokens_on_discarded_at"
    t.index ["job_vacancy_id"], name: "index_shared_vacancy_tokens_on_job_vacancy_id"
    t.index ["token"], name: "index_shared_vacancy_tokens_on_token", unique: true
  end

  create_table "skills", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_skills_on_discarded_at"
  end

  create_table "specializations", force: :cascade do |t|
    t.string "name"
    t.bigint "career_path_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "job_role_id"
    t.index ["career_path_id"], name: "index_specializations_on_career_path_id"
    t.index ["discarded_at"], name: "index_specializations_on_discarded_at"
    t.index ["job_role_id"], name: "index_specializations_on_job_role_id"
    t.index ["name", "career_path_id"], name: "index_specializations_on_name_and_career_path_id", unique: true
    t.index ["name", "job_role_id"], name: "index_specializations_on_name_and_job_role_id", unique: true
  end

  create_table "stimuli", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.bigint "module_session_id"
    t.integer "duration"
    t.integer "stimulus_question_duration"
    t.integer "order_level", default: 0, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "config", default: {}, null: false
    t.index ["discarded_at"], name: "index_stimuli_on_discarded_at"
    t.index ["module_session_id"], name: "index_stimuli_on_module_session_id"
    t.index ["order_level", "module_session_id"], name: "index_stimuli_on_order_level_and_module_session_id", unique: true
  end

  create_table "sub_competencies", force: :cascade do |t|
    t.string "name"
    t.datetime "discarded_at", precision: nil
    t.bigint "competency_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "external_id"
    t.bigint "bloom_taxonomy_max_level_id"
    t.index ["bloom_taxonomy_max_level_id"], name: "index_sub_competencies_on_bloom_taxonomy_max_level_id"
    t.index ["competency_id", "name"], name: "index_sub_competencies_on_competency_id_and_name", unique: true
    t.index ["competency_id"], name: "index_sub_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_sub_competencies_on_discarded_at"
    t.index ["external_id"], name: "index_sub_competencies_on_external_id", unique: true
  end

  create_table "sub_competency_specializations", force: :cascade do |t|
    t.bigint "sub_competency_id", null: false
    t.bigint "specialization_id", null: false
    t.bigint "max_level_bloom_taxonomy_id", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_sub_competency_specializations_on_discarded_at"
    t.index ["max_level_bloom_taxonomy_id"], name: "index_sub_competency_specialization_max_bt"
    t.index ["specialization_id"], name: "index_sub_competency_specializations_on_specialization_id"
    t.index ["sub_competency_id", "specialization_id", "max_level_bloom_taxonomy_id"], name: "index_sub_competency_specializations", unique: true
    t.index ["sub_competency_id"], name: "index_sub_competency_specializations_on_sub_competency_id"
  end

  create_table "survey_answers", force: :cascade do |t|
    t.bigint "survey_id"
    t.bigint "user_id"
    t.bigint "survey_component_id"
    t.string "survey_option_ids", default: [], array: true
    t.string "answer"
    t.string "file_urls", array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_survey_answers_on_discarded_at"
    t.index ["survey_component_id"], name: "index_survey_answers_on_survey_component_id"
    t.index ["survey_id", "user_id", "survey_component_id"], name: "index_survey_answers_on_some_columns", unique: true
    t.index ["survey_id"], name: "index_survey_answers_on_survey_id"
    t.index ["survey_option_ids"], name: "index_survey_answers_on_survey_option_ids"
    t.index ["user_id"], name: "index_survey_answers_on_user_id"
  end

  create_table "survey_components", force: :cascade do |t|
    t.bigint "survey_template_id"
    t.string "title", null: false
    t.enum "component_type", enum_type: "survey_component_type"
    t.text "description"
    t.integer "order_level", null: false
    t.boolean "show_description", default: false
    t.boolean "required", default: false
    t.integer "bottom_scale"
    t.integer "upper_scale"
    t.integer "maximum_number_of_file"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "include_other_option"
    t.string "image_urls", default: [], array: true
    t.index ["discarded_at"], name: "index_survey_components_on_discarded_at"
    t.index ["survey_template_id"], name: "index_survey_components_on_survey_template_id"
  end

  create_table "survey_options", force: :cascade do |t|
    t.bigint "survey_component_id"
    t.string "option"
    t.enum "option_type", enum_type: "survey_option_type"
    t.integer "order_level"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_survey_options_on_discarded_at"
    t.index ["survey_component_id"], name: "index_survey_options_on_survey_component_id"
  end

  create_table "survey_templates", force: :cascade do |t|
    t.string "name", null: false
    t.enum "survey_type", enum_type: "survey_type"
    t.text "description"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "used_count", default: 0
    t.index ["discarded_at"], name: "index_survey_templates_on_discarded_at"
  end

  create_table "surveys", force: :cascade do |t|
    t.bigint "survey_template_id"
    t.string "survey_type"
    t.string "target_type"
    t.datetime "starts_at", precision: nil
    t.datetime "started_at", precision: nil
    t.integer "started_by"
    t.string "object_type"
    t.bigint "object_id"
    t.boolean "use_default_template_for_module", default: true
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "progress", default: 0
    t.string "name"
    t.datetime "ends_at", precision: nil
    t.index ["discarded_at"], name: "index_surveys_on_discarded_at"
    t.index ["object_type", "object_id"], name: "index_surveys_on_object_type_and_object_id"
    t.index ["survey_template_id"], name: "index_surveys_on_survey_template_id"
    t.index ["survey_type", "target_type", "object_id", "object_type"], name: "index_surveys_on_some_columns", unique: true
  end

  create_table "taggings", force: :cascade do |t|
    t.bigint "bloom_taxonomy_id"
    t.bigint "competency_id"
    t.bigint "sub_competency_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["bloom_taxonomy_id", "competency_id", "sub_competency_id"], name: "unique_index_taggings", unique: true
    t.index ["bloom_taxonomy_id"], name: "index_taggings_on_bloom_taxonomy_id"
    t.index ["competency_id"], name: "index_taggings_on_competency_id"
    t.index ["discarded_at"], name: "index_taggings_on_discarded_at"
    t.index ["sub_competency_id"], name: "index_taggings_on_sub_competency_id"
  end

  create_table "talent_access_token_histories", force: :cascade do |t|
    t.bigint "talent_access_token_id"
    t.string "token"
    t.datetime "expired_at", precision: nil
    t.integer "accessed_count", default: 0
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_talent_access_token_histories_on_discarded_at"
    t.index ["talent_access_token_id"], name: "index_talent_access_token_histories_on_talent_access_token_id"
  end

  create_table "talent_access_token_visits", force: :cascade do |t|
    t.bigint "talent_access_token_id"
    t.bigint "talent_access_token_history_id"
    t.string "token"
    t.integer "accessed_count", default: 0
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_talent_access_token_visits_on_discarded_at"
    t.index ["talent_access_token_history_id"], name: "index_talent_access_token_visits_token_id"
    t.index ["talent_access_token_id"], name: "index_talent_access_token_visits_token_history_id"
  end

  create_table "talent_access_tokens", force: :cascade do |t|
    t.string "token"
    t.bigint "user_id"
    t.bigint "partner_id"
    t.datetime "expired_at", precision: nil
    t.integer "accessed_count", default: 1
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_talent_access_tokens_on_discarded_at"
    t.index ["partner_id"], name: "index_talent_access_tokens_on_partner_id"
    t.index ["token"], name: "index_talent_access_tokens_on_token"
    t.index ["user_id", "partner_id"], name: "index_unique_talent_access_tokens_user_partner", unique: true
    t.index ["user_id"], name: "index_talent_access_tokens_on_user_id"
  end

  create_table "talent_data_crawling_requests", force: :cascade do |t|
    t.string "csv_file_url", null: false
    t.bigint "request_by_id", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["csv_file_url"], name: "index_talent_data_crawling_requests_on_csv_file_url", unique: true
    t.index ["discarded_at"], name: "index_talent_data_crawling_requests_on_discarded_at"
    t.index ["request_by_id"], name: "index_talent_data_crawling_requests_on_request_by_id"
  end

  create_table "talent_data_crawlings", force: :cascade do |t|
    t.bigint "request_id", null: false
    t.bigint "user_id", null: false
    t.text "response_text"
    t.string "response_status"
    t.integer "credit_used", default: 0, null: false
    t.string "status"
    t.jsonb "updated_data", default: {}, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_talent_data_crawlings_on_discarded_at"
    t.index ["request_id"], name: "index_talent_data_crawlings_on_request_id"
    t.index ["user_id"], name: "index_talent_data_crawlings_on_user_id"
  end

  create_table "talent_recommendations", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "job_role_group_id", null: false
    t.text "notes"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "recommended_by_id"
    t.index ["discarded_at"], name: "index_talent_recommendations_on_discarded_at"
    t.index ["job_role_group_id"], name: "index_talent_recommendations_on_job_role_group_id"
    t.index ["recommended_by_id"], name: "index_talent_recommendations_on_recommended_by_id"
    t.index ["user_id"], name: "index_talent_recommendations_on_user_id"
  end

  create_table "ti_analytic_comparisons", force: :cascade do |t|
    t.bigint "variable_id", null: false
    t.string "name", null: false
    t.float "value", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_ti_analytic_comparisons_on_discarded_at"
    t.index ["variable_id", "name"], name: "index_ti_analytic_comparisons_on_variable_id_and_name", unique: true
    t.index ["variable_id"], name: "index_ti_analytic_comparisons_on_variable_id"
  end

  create_table "ti_analytic_projects", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.string "title"
    t.string "context"
    t.bigint "x_variable_id", null: false
    t.bigint "y_variable_id", null: false
    t.jsonb "config", default: {}, null: false
    t.jsonb "metadata", default: {}, null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_ti_analytic_projects_on_discarded_at"
    t.index ["partner_id"], name: "index_ti_analytic_projects_on_partner_id"
    t.index ["x_variable_id"], name: "index_ti_analytic_projects_on_x_variable_id"
    t.index ["y_variable_id"], name: "index_ti_analytic_projects_on_y_variable_id"
  end

  create_table "ti_idp_project_users", force: :cascade do |t|
    t.bigint "ti_idp_project_id", null: false
    t.bigint "user_id", null: false
    t.jsonb "competency_gaps", default: [], null: false, array: true
    t.string "objectives", default: [], null: false, array: true
    t.jsonb "ai_recommendation", default: {}, null: false
    t.string "status", default: "queued", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_ti_idp_project_users_on_discarded_at"
    t.index ["ti_idp_project_id"], name: "index_ti_idp_project_users_on_ti_idp_project_id"
    t.index ["user_id"], name: "index_ti_idp_project_users_on_user_id"
  end

  create_table "ti_idp_projects", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.string "name"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "status", default: "queued", null: false
    t.index ["discarded_at"], name: "index_ti_idp_projects_on_discarded_at"
    t.index ["partner_id"], name: "index_ti_idp_projects_on_partner_id"
  end

  create_table "universities", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.string "university_logo_url"
    t.boolean "jap_university", default: false
    t.bigint "location_id"
    t.bigint "province_id"
    t.string "abbreviation_name"
    t.boolean "verified", default: true, null: false
    t.integer "public_location_id"
    t.index ["discarded_at"], name: "index_universities_on_discarded_at"
    t.index ["location_id"], name: "index_universities_on_location_id"
    t.index ["name"], name: "index_universities_on_name", unique: true
    t.index ["province_id"], name: "index_universities_on_province_id"
    t.index ["public_location_id"], name: "index_universities_on_public_location_id"
  end

  create_table "university_majors", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.boolean "verified", default: true, null: false
    t.index ["discarded_at"], name: "index_university_majors_on_discarded_at"
    t.index ["name"], name: "index_university_majors_on_name", unique: true
  end

  create_table "user_aggregates", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "experiences_count", default: 0, null: false
    t.integer "talent_recommendations_count", default: 0, null: false
    t.integer "digital_cv_visitors_count", default: 0, null: false
    t.integer "digital_cv_anonymous_visitors_count", default: 0, null: false
    t.boolean "is_new_applicant", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "digital_cv_progress_order", default: 0, null: false
    t.integer "status_availability_order", default: 0, null: false
    t.float "digital_cv_score", default: 0.0, null: false
    t.integer "calculated_years_experience", default: 0
    t.integer "apply_counter", default: 0
    t.float "bmi_score"
    t.index ["digital_cv_anonymous_visitors_count"], name: "index_user_aggregates_on_digital_cv_anonymous_visitors_count"
    t.index ["digital_cv_progress_order"], name: "index_user_aggregates_on_digital_cv_progress_order"
    t.index ["digital_cv_visitors_count"], name: "index_user_aggregates_on_digital_cv_visitors_count"
    t.index ["experiences_count"], name: "index_user_aggregates_on_experiences_count"
    t.index ["is_new_applicant"], name: "index_user_aggregates_on_is_new_applicant"
    t.index ["status_availability_order"], name: "index_user_aggregates_on_status_availability_order"
    t.index ["talent_recommendations_count"], name: "index_user_aggregates_on_talent_recommendations_count"
    t.index ["user_id"], name: "index_user_aggregates_on_user_id", unique: true
  end

  create_table "user_assessments", force: :cascade do |t|
    t.string "assessment_external_id"
    t.bigint "user_id", null: false
    t.string "assessment_service"
    t.string "assessment_type"
    t.string "status"
    t.datetime "completed_at", precision: nil
    t.string "external_link"
    t.string "external_id"
    t.float "score"
    t.jsonb "sub_scores"
    t.jsonb "raw_data"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assessment_external_id", "user_id", "assessment_service", "assessment_type"], name: "index_user_assessment_on_external_assessment", unique: true
    t.index ["discarded_at"], name: "index_user_assessments_on_discarded_at"
    t.index ["user_id"], name: "index_user_assessments_on_user_id"
  end

  create_table "user_biodata", force: :cascade do |t|
    t.boolean "live_at_id_card_address"
    t.string "state", default: "in_progress", null: false
    t.jsonb "english_certification", default: {"type"=>nil, "score"=>nil, "result_url"=>nil, "institution"=>nil}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.bigint "user_id"
    t.index ["discarded_at"], name: "index_user_biodata_on_discarded_at"
    t.index ["state"], name: "index_user_biodata_on_state"
    t.index ["user_id"], name: "index_user_biodata_on_user_id", unique: true
  end

  create_table "user_career_paths", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "career_path_id"
    t.string "tier", default: "bronze", null: false
    t.float "progress", default: 0.0
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "journey_phase"
    t.float "journey_progress", default: 0.0
    t.string "learning_level"
    t.jsonb "skill_list", default: []
    t.string "product_recommendation"
    t.string "estimation_plan"
    t.string "target"
    t.string "page_state"
    t.float "employability_score"
    t.float "job_matching_progress_starts_at", default: 0.0
    t.float "job_matching_progress_ends_at", default: 0.0
    t.index ["career_path_id"], name: "index_user_career_paths_on_career_path_id"
    t.index ["discarded_at"], name: "index_user_career_paths_on_discarded_at"
    t.index ["user_id", "career_path_id"], name: "index_user_career_paths_on_user_id_and_career_path_id", unique: true
    t.index ["user_id"], name: "index_user_career_paths_on_user_id"
  end

  create_table "user_change_emails", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "email_before"
    t.string "email_after"
    t.string "status", default: "waiting_confirmation"
    t.datetime "email_changed_at", precision: nil
    t.datetime "email_confirmation_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_change_emails_on_discarded_at"
    t.index ["user_id"], name: "index_user_change_emails_on_user_id"
  end

  create_table "user_competencies", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "competency_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "bloom_taxonomy_max_level_id"
    t.bigint "bloom_taxonomy_min_level_id"
    t.float "score", default: 0.0
    t.index ["bloom_taxonomy_max_level_id"], name: "index_user_competencies_on_bloom_taxonomy_max_level_id"
    t.index ["bloom_taxonomy_min_level_id"], name: "index_user_competencies_on_bloom_taxonomy_min_level_id"
    t.index ["competency_id"], name: "index_user_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_user_competencies_on_discarded_at"
    t.index ["user_id", "competency_id"], name: "index_user_competencies_on_user_id_and_competency_id", unique: true
    t.index ["user_id"], name: "index_user_competencies_on_user_id"
  end

  create_table "user_course_counselings", force: :cascade do |t|
    t.bigint "counselor_id"
    t.bigint "user_course_id"
    t.datetime "counseling_date", precision: nil
    t.string "necessity"
    t.text "discuss"
    t.string "state", default: "scheduled"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["counselor_id"], name: "index_user_course_counselings_on_counselor_id"
    t.index ["discarded_at"], name: "index_user_course_counselings_on_discarded_at"
    t.index ["user_course_id"], name: "index_user_course_counselings_on_user_course_id"
  end

  create_table "user_course_reviews", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "course_id", null: false
    t.text "review", default: "", null: false
    t.integer "rating", default: 0, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "mood"
    t.index ["course_id"], name: "index_user_course_reviews_on_course_id"
    t.index ["discarded_at"], name: "index_user_course_reviews_on_discarded_at"
    t.index ["user_id", "course_id"], name: "index_user_course_reviews_on_user_id_and_course_id", unique: true
    t.index ["user_id"], name: "index_user_course_reviews_on_user_id"
  end

  create_table "user_courses", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "course_id"
    t.string "certificate_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.enum "state", default: "enrolled", enum_type: "user_course_state"
    t.datetime "discarded_at", precision: nil
    t.bigint "job_guarantee_id"
    t.boolean "course_coordinator", default: false, null: false
    t.string "role", default: "student"
    t.integer "total_xp", default: 0
    t.string "questionnaire_answer", default: [], array: true
    t.string "onboard_state", default: "in_progress"
    t.float "progress_percentage", default: 0.0, null: false
    t.string "reward_onboard_state", default: "in_progress"
    t.float "score", default: 0.0
    t.float "duration_completed", default: 0.0
    t.index ["course_id"], name: "index_user_courses_on_course_id"
    t.index ["discarded_at"], name: "index_user_courses_on_discarded_at"
    t.index ["job_guarantee_id"], name: "index_user_courses_on_job_guarantee_id"
    t.index ["role"], name: "index_user_courses_on_role"
    t.index ["user_id", "course_id"], name: "index_user_courses_on_user_id_and_course_id", unique: true
    t.index ["user_id"], name: "index_user_courses_on_user_id"
  end

  create_table "user_digital_cv_anonymous_visitors", force: :cascade do |t|
    t.bigint "visited_user_id"
    t.datetime "last_visited_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_digital_cv_anonymous_visitors_on_discarded_at"
    t.index ["visited_user_id"], name: "index_user_digital_cv_anonymous_visitors_on_visited_user_id"
  end

  create_table "user_digital_cv_settings", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "unique_address", null: false
    t.jsonb "configurable_access_status", default: {"users_about_me_status"=>true, "users_profile_picture_status"=>true}, null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "public_access_status", default: true
    t.index ["discarded_at"], name: "index_user_digital_cv_settings_on_discarded_at"
    t.index ["unique_address"], name: "index_user_digital_cv_settings_on_unique_address", unique: true
    t.index ["user_id"], name: "index_user_digital_cv_settings_on_user_id", unique: true
  end

  create_table "user_digital_cv_visitors", force: :cascade do |t|
    t.bigint "visited_user_id"
    t.bigint "visitor_user_id"
    t.datetime "last_visited_at", precision: nil
    t.boolean "valid_status", default: true
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_digital_cv_visitors_on_discarded_at"
    t.index ["visited_user_id", "visitor_user_id"], name: "index_digital_cv_visited_visitor_user", unique: true
    t.index ["visited_user_id"], name: "index_user_digital_cv_visitors_on_visited_user_id"
    t.index ["visitor_user_id"], name: "index_user_digital_cv_visitors_on_visitor_user_id"
  end

  create_table "user_digital_cvs", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.jsonb "suggestions", default: [], null: false, array: true
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_digital_cvs_on_discarded_at"
    t.index ["user_id"], name: "index_user_digital_cvs_on_user_id"
  end

  create_table "user_documents", force: :cascade do |t|
    t.string "title"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "admin_id"
    t.string "document_url"
    t.datetime "discarded_at", precision: nil
    t.string "document_type"
    t.index ["admin_id"], name: "index_user_documents_on_admin_id"
    t.index ["discarded_at"], name: "index_user_documents_on_discarded_at"
    t.index ["document_type"], name: "index_user_documents_on_document_type"
    t.index ["user_id", "document_type"], name: "index_user_documents_on_user_id_and_document_type", unique: true
    t.index ["user_id"], name: "index_user_documents_on_user_id"
  end

  create_table "user_events", force: :cascade do |t|
    t.bigint "user_id"
    t.string "event_name"
    t.string "event_description"
    t.string "start_date"
    t.string "end_date"
    t.string "hangout_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "event_id"
    t.datetime "discarded_at", precision: nil
    t.enum "event_type", enum_type: "event_type"
    t.bigint "module_session_id"
    t.integer "day_timestamp"
    t.string "email"
    t.bigint "vwx_task_id"
    t.bigint "job_guarantee_id"
    t.bigint "course_id"
    t.index ["course_id", "event_type", "user_id"], name: "index_user_events_on_course_id_event_type_user_id", unique: true
    t.index ["course_id"], name: "index_user_events_on_course_id"
    t.index ["discarded_at"], name: "index_user_events_on_discarded_at"
    t.index ["email", "event_type", "day_timestamp"], name: "user_event_type_per_email_per_day", unique: true
    t.index ["job_guarantee_id"], name: "index_user_events_on_job_guarantee_id"
    t.index ["module_session_id"], name: "index_user_events_on_module_session_id"
    t.index ["user_id", "job_guarantee_id", "event_type", "day_timestamp"], name: "user_event_type_per_jap_per_day", unique: true
    t.index ["user_id", "module_session_id", "event_type", "day_timestamp"], name: "user_event_type_per_session_per_day", unique: true
    t.index ["user_id", "vwx_task_id", "event_type", "day_timestamp"], name: "user_event_type_per_task_per_day", unique: true
    t.index ["user_id"], name: "index_user_events_on_user_id"
    t.index ["vwx_task_id"], name: "index_user_events_on_vwx_task_id"
  end

  create_table "user_feature_onboardings", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "feature_id", null: false
    t.string "state"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_feature_onboardings_on_discarded_at"
    t.index ["feature_id"], name: "index_user_feature_onboardings_on_feature_id"
    t.index ["user_id", "feature_id"], name: "index_user_feature_onboardings_on_user_id_and_feature_id", unique: true
    t.index ["user_id"], name: "index_user_feature_onboardings_on_user_id"
  end

  create_table "user_feedbacks", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "feedback_template_id", null: false
    t.integer "rating"
    t.string "feedbacks", default: [], null: false, array: true
    t.jsonb "additional_context", default: {}, null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_feedbacks_on_discarded_at"
    t.index ["feedback_template_id"], name: "index_user_feedbacks_on_feedback_template_id"
    t.index ["user_id"], name: "index_user_feedbacks_on_user_id"
  end

  create_table "user_gcalendar_events", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "gcalendar_event_id", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "response_status", default: "needsAction"
    t.datetime "response_status_updated_at", precision: nil
    t.index ["discarded_at"], name: "index_user_gcalendar_events_on_discarded_at"
    t.index ["gcalendar_event_id"], name: "index_user_gcalendar_events_on_gcalendar_event_id"
    t.index ["response_status_updated_at"], name: "index_user_gcalendar_events_on_response_status_updated_at"
    t.index ["user_id", "gcalendar_event_id"], name: "index_user_gcalendar_events_on_user_id_and_gcalendar_event_id", unique: true
    t.index ["user_id"], name: "index_user_gcalendar_events_on_user_id"
  end

  create_table "user_groups", force: :cascade do |t|
    t.bigint "group_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "assigned_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.string "note"
    t.index ["discarded_at"], name: "index_user_groups_on_discarded_at"
    t.index ["group_id", "user_id"], name: "index_user_groups_on_group_id_and_user_id", unique: true
    t.index ["group_id"], name: "index_user_groups_on_group_id"
    t.index ["user_id"], name: "index_user_groups_on_user_id"
  end

  create_table "user_hiring_pipeline_feedbacks", force: :cascade do |t|
    t.bigint "user_hiring_pipeline_id", null: false
    t.bigint "user_id", null: false
    t.text "content"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "user_pipeline_status", default: "in_pipeline", null: false
    t.index ["discarded_at"], name: "index_user_hiring_pipeline_feedbacks_on_discarded_at"
    t.index ["user_hiring_pipeline_id"], name: "index_user_hiring_pipeline_feedbacks_on_user_hiring_pipeline_id"
    t.index ["user_id"], name: "index_user_hiring_pipeline_feedbacks_on_user_id"
  end

  create_table "user_hiring_pipeline_submissions", force: :cascade do |t|
    t.bigint "user_hiring_pipeline_id", null: false
    t.string "submission_url"
    t.datetime "due_date", precision: nil
    t.datetime "submitted_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_hiring_pipeline_submissions_on_discarded_at"
    t.index ["user_hiring_pipeline_id"], name: "index_user_hiring_pipeline_submissions_on_uhp_id", unique: true
  end

  create_table "user_hiring_pipelines", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "hiring_pipeline_id"
    t.jsonb "metadata", default: {}
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "feedbacks_count", default: 0, null: false
    t.integer "feedbacks_count_in_rejected", default: 0, null: false
    t.index ["discarded_at"], name: "index_user_hiring_pipelines_on_discarded_at"
    t.index ["hiring_pipeline_id"], name: "index_user_hiring_pipelines_on_hiring_pipeline_id"
    t.index ["user_id", "hiring_pipeline_id"], name: "index_user_hiring_pipelines_on_user_id_and_hiring_pipeline_id", unique: true
    t.index ["user_id"], name: "index_user_hiring_pipelines_on_user_id"
  end

  create_table "user_homework_questions", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "homework_question_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_homework_questions_on_discarded_at"
    t.index ["homework_question_id", "user_id"], name: "unique_index_question_user_on_user_homework_questions", unique: true
    t.index ["homework_question_id"], name: "index_user_homework_questions_on_homework_question_id"
    t.index ["user_id"], name: "index_user_homework_questions_on_user_id"
  end

  create_table "user_homework_taggings", force: :cascade do |t|
    t.integer "score", default: 0, null: false
    t.bigint "homework_question_tagging_id"
    t.bigint "user_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_homework_taggings_on_discarded_at"
    t.index ["homework_question_tagging_id"], name: "index_user_homework_taggings_on_homework_question_tagging_id"
    t.index ["user_id", "homework_question_tagging_id"], name: "index_user_homework_taggings_on_user_and_homework_tagging", unique: true
    t.index ["user_id"], name: "index_user_homework_taggings_on_user_id"
  end

  create_table "user_import_results", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "file_url", null: false
    t.string "status", default: "in_progress", null: false
    t.string "error_message"
    t.jsonb "error_details", default: [], null: false, array: true
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "change_details", default: {}, null: false
    t.index ["discarded_at"], name: "index_user_import_results_on_discarded_at"
    t.index ["user_id"], name: "index_user_import_results_on_user_id"
  end

  create_table "user_installments", force: :cascade do |t|
    t.string "external_id"
    t.bigint "user_id"
    t.bigint "course_id"
    t.enum "status", enum_type: "installment_status"
    t.datetime "start_date", precision: nil
    t.integer "total_installment"
    t.string "recurring_id"
    t.enum "interval", enum_type: "interval_type"
    t.integer "interval_count"
    t.enum "missed_payment_action", enum_type: "missed_payment_action"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.text "description"
    t.bigint "installment_request_id"
    t.bigint "jgp_promo_id"
    t.index ["course_id"], name: "index_user_installments_on_course_id"
    t.index ["discarded_at"], name: "index_user_installments_on_discarded_at"
    t.index ["external_id"], name: "index_user_installments_on_external_id", unique: true
    t.index ["installment_request_id"], name: "index_user_installments_on_installment_request_id"
    t.index ["jgp_promo_id"], name: "index_user_installments_on_jgp_promo_id"
    t.index ["user_id"], name: "index_user_installments_on_user_id"
  end

  create_table "user_invoices", force: :cascade do |t|
    t.string "external_id"
    t.string "invoice_id"
    t.bigint "user_id"
    t.bigint "course_id"
    t.enum "status", default: "pending", enum_type: "invoice_status"
    t.enum "payment_method", enum_type: "payment_method"
    t.enum "payment_channel", enum_type: "payment_channel"
    t.integer "invoice_duration", default: 3600, null: false
    t.datetime "start_date", precision: nil
    t.datetime "paid_at", precision: nil
    t.string "invoice_url"
    t.bigint "user_installment_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.text "description"
    t.integer "recurring_amount", default: 0, null: false
    t.integer "paid_amount", default: 0, null: false
    t.boolean "extended_invoice", default: false, null: false
    t.boolean "notified", default: false, null: false
    t.bigint "installment_request_id"
    t.boolean "is_down_payment", default: false, null: false
    t.boolean "is_jgp", default: false, null: false
    t.float "amount", default: 0.0, null: false
    t.boolean "migrated", default: false, null: false
    t.text "action_note"
    t.float "late_discount", default: 0.0
    t.bigint "vwx_id"
    t.integer "used_rakamin_point", default: 0, null: false
    t.boolean "basic_access", default: false, null: false
    t.bigint "job_guarantee_id"
    t.integer "order_level", default: 0, null: false
    t.index ["course_id"], name: "index_user_invoices_on_course_id"
    t.index ["discarded_at"], name: "index_user_invoices_on_discarded_at"
    t.index ["external_id"], name: "index_user_invoices_on_external_id", unique: true
    t.index ["installment_request_id"], name: "index_user_invoices_on_installment_request_id"
    t.index ["job_guarantee_id"], name: "index_user_invoices_on_job_guarantee_id"
    t.index ["user_id"], name: "index_user_invoices_on_user_id"
    t.index ["user_installment_id"], name: "index_user_invoices_on_user_installment_id"
    t.index ["vwx_id"], name: "index_user_invoices_on_vwx_id"
  end

  create_table "user_job_role_groups", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "job_role_group_id", null: false
    t.integer "partner_ids", default: [], null: false, array: true
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_job_role_groups_on_discarded_at"
    t.index ["job_role_group_id"], name: "index_user_job_role_groups_on_job_role_group_id"
    t.index ["user_id", "job_role_group_id"], name: "index_user_job_role_groups_on_user_id_and_job_role_group_id", unique: true
    t.index ["user_id"], name: "index_user_job_role_groups_on_user_id"
  end

  create_table "user_job_vacancies", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "job_vacancy_id"
    t.string "state", default: "pool"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.text "description"
    t.datetime "invited_at", precision: nil
    t.text "message"
    t.string "reject_reasons", default: [], array: true
    t.string "notes_for_rakamin"
    t.string "resume_url"
    t.jsonb "documents", default: [], null: false
    t.string "company_contact_email"
    t.string "company_contact_phone"
    t.datetime "contacted_at", precision: nil
    t.datetime "invitation_accepted_at", precision: nil
    t.datetime "hr_interview_at", precision: nil
    t.datetime "user_interview_at", precision: nil
    t.datetime "offering_at", precision: nil
    t.datetime "technical_pretest_at", precision: nil
    t.datetime "rejected_at", precision: nil
    t.datetime "selected_at", precision: nil
    t.bigint "rejected_by_id"
    t.integer "match_score"
    t.jsonb "unmatched_data", default: {}
    t.string "utm_source"
    t.datetime "applied_at", precision: nil
    t.bigint "offering_by_id"
    t.bigint "hr_interview_by_id"
    t.bigint "user_interview_by_id"
    t.bigint "technical_pretest_by_id"
    t.bigint "selected_by_id"
    t.bigint "invited_by_id"
    t.string "test_result_url"
    t.jsonb "metadata", default: {}, null: false
    t.string "cv_screening_recommendation", default: "pending"
    t.string "final_recommendation", default: "pending"
    t.bigint "job_vacancy_tagging_id"
    t.bigint "partner_placement_id"
    t.jsonb "additional_data", default: []
    t.index ["discarded_at"], name: "index_user_job_vacancies_on_discarded_at"
    t.index ["hr_interview_by_id"], name: "index_user_job_vacancies_on_hr_interview_by_id"
    t.index ["invited_by_id"], name: "index_user_job_vacancies_on_invited_by_id"
    t.index ["job_vacancy_id"], name: "index_user_job_vacancies_on_job_vacancy_id"
    t.index ["job_vacancy_tagging_id"], name: "index_user_job_vacancies_on_job_vacancy_tagging_id"
    t.index ["match_score"], name: "index_user_job_vacancies_on_match_score"
    t.index ["offering_by_id"], name: "index_user_job_vacancies_on_offering_by_id"
    t.index ["partner_placement_id"], name: "index_user_job_vacancies_on_partner_placement_id"
    t.index ["rejected_by_id"], name: "index_user_job_vacancies_on_rejected_by_id"
    t.index ["selected_at"], name: "index_user_job_vacancies_on_selected_at"
    t.index ["selected_by_id"], name: "index_user_job_vacancies_on_selected_by_id"
    t.index ["state"], name: "index_user_job_vacancies_on_state"
    t.index ["technical_pretest_by_id"], name: "index_user_job_vacancies_on_technical_pretest_by_id"
    t.index ["user_id", "job_vacancy_id"], name: "index_user_job_vacancies_on_user_id_and_job_vacancy_id", unique: true
    t.index ["user_id"], name: "index_user_job_vacancies_on_user_id"
    t.index ["user_interview_by_id"], name: "index_user_job_vacancies_on_user_interview_by_id"
    t.index ["utm_source"], name: "index_user_job_vacancies_on_utm_source"
  end

  create_table "user_job_vacancy_assessments", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "job_vacancy_assessment_id", null: false
    t.string "status", default: "waiting_review", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "due_date", precision: nil
    t.bigint "user_token_id"
    t.boolean "is_new", default: false, null: false
    t.text "external_result_url"
    t.bigint "user_assessment_id"
    t.string "qualification", default: "pending"
    t.datetime "completed_at", precision: nil
    t.string "assessment_recommendation", default: "pending"
    t.string "level_score", default: "pending"
    t.jsonb "state_histories", default: {}
    t.integer "duration", default: 0
    t.index ["discarded_at"], name: "index_user_job_vacancy_assessments_on_discarded_at"
    t.index ["job_vacancy_assessment_id"], name: "index_user_job_vacancy_assessments_on_job_vacancy_assessment_id"
    t.index ["qualification"], name: "index_user_job_vacancy_assessments_on_qualification"
    t.index ["status"], name: "index_user_job_vacancy_assessments_on_status"
    t.index ["user_assessment_id"], name: "index_user_job_vacancy_assessments_on_user_assessment_id"
    t.index ["user_id", "job_vacancy_assessment_id"], name: "index_user_job_vacancy_assessment", unique: true
    t.index ["user_id"], name: "index_user_job_vacancy_assessments_on_user_id"
    t.index ["user_token_id"], name: "index_user_job_vacancy_assessments_on_user_token_id"
  end

  create_table "user_mentoring_groups", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "mentoring_group_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_user_mentoring_groups_on_discarded_at"
    t.index ["mentoring_group_id"], name: "index_user_mentoring_groups_on_mentoring_group_id"
    t.index ["user_id", "mentoring_group_id"], name: "index_user_mentoring_groups_on_user_id_and_mentoring_group_id", unique: true
    t.index ["user_id"], name: "index_user_mentoring_groups_on_user_id"
  end

  create_table "user_module_sessions", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "module_session_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.enum "status_completed", default: "not_started", enum_type: "status_completed"
    t.datetime "discarded_at", precision: nil
    t.datetime "starts_at", precision: nil
    t.datetime "completed_at", precision: nil
    t.integer "earned_xp", default: 0
    t.datetime "last_accessed_at", precision: nil
    t.datetime "extra_time_ends_at", precision: nil
    t.bigint "last_visited_question_id"
    t.datetime "timeout_at", precision: nil
    t.string "onboard_state", default: "in_progress"
    t.bigint "bundling_code_id"
    t.jsonb "config", default: {}
    t.integer "submit_count", default: 0
    t.index ["bundling_code_id"], name: "index_user_module_sessions_on_bundling_code_id"
    t.index ["discarded_at"], name: "index_user_module_sessions_on_discarded_at"
    t.index ["last_visited_question_id"], name: "index_user_module_sessions_on_last_visited_question_id"
    t.index ["module_session_id"], name: "index_user_module_sessions_on_module_session_id"
    t.index ["user_id", "module_session_id"], name: "index_user_module_sessions_on_user_id_and_module_session_id", unique: true
    t.index ["user_id"], name: "index_user_module_sessions_on_user_id"
  end

  create_table "user_notifications", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.text "message"
    t.string "state", default: "unread"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.string "icon_object_type"
    t.bigint "icon_object_id"
    t.string "icon_object_column"
    t.string "icon_url"
    t.jsonb "metadata", default: {}, null: false
    t.index ["discarded_at"], name: "index_user_notifications_on_discarded_at"
    t.index ["icon_object_type", "icon_object_id"], name: "index_user_notifications_on_icon_object_type_and_icon_object_id"
    t.index ["user_id"], name: "index_user_notifications_on_user_id"
  end

  create_table "user_performances", force: :cascade do |t|
    t.bigint "user_id"
    t.string "performance_type"
    t.text "objective"
    t.text "detail"
    t.float "completion_achievement"
    t.string "period_type"
    t.datetime "starts_at"
    t.datetime "ends_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_performances_on_user_id"
  end

  create_table "user_partner_competencies", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "partner_competency_id", null: false
    t.float "score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_user_partner_competencies_on_discarded_at"
    t.index ["partner_competency_id"], name: "index_user_partner_competencies_on_partner_competency_id"
    t.index ["user_id"], name: "index_user_partner_competencies_on_user_id"
  end

  create_table "user_partner_competency_histories", force: :cascade do |t|
    t.bigint "user_partner_competency_id", null: false
    t.date "evaluation_start_date"
    t.date "evaluation_end_date"
    t.float "score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_user_partner_competency_histories_on_discarded_at"
    t.index ["user_partner_competency_id"], name: "idx_upch_on_user_partner_competency_id"
  end

  create_table "user_personalities", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "aspect"
    t.text "description"
    t.float "score"
    t.float "max_score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at"
    t.index ["discarded_at"], name: "index_user_personalities_on_discarded_at"
    t.index ["user_id"], name: "index_user_personalities_on_user_id"
  end
  create_table "user_partner_attributes", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "partner_attribute_id", null: false
    t.string "string_value"
    t.float "float_value"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_partner_attributes_on_discarded_at"
    t.index ["partner_attribute_id"], name: "index_user_partner_attributes_on_partner_attribute_id"
    t.index ["user_id", "partner_attribute_id"], name: "index_user_partner_attributes_on_user_and_partner_attribute", unique: true
    t.index ["user_id"], name: "index_user_partner_attributes_on_user_id"
  end

  create_table "user_preferences", force: :cascade do |t|
    t.integer "age"
    t.string "latest_education"
    t.string "education_field"
    t.string "job_experience"
    t.string "job_field"
    t.string "interested_job_field"
    t.string "custom_interested_job_field"
    t.string "state", default: "in_progress"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["discarded_at"], name: "index_user_preferences_on_discarded_at"
    t.index ["user_id"], name: "index_user_preferences_on_user_id", unique: true
  end

  create_table "user_proctor_logs", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "course_id", null: false
    t.string "log_type", null: false
    t.string "description"
    t.string "captured_record_link"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id"], name: "index_user_proctor_logs_on_course_id"
    t.index ["description", "log_type", "user_id", "course_id"], name: "index_user_proctor_logs_on_4_columns"
    t.index ["discarded_at"], name: "index_user_proctor_logs_on_discarded_at"
    t.index ["user_id"], name: "index_user_proctor_logs_on_user_id"
  end

  create_table "user_proctor_reports", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "course_id", null: false
    t.string "description"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id"], name: "index_user_proctor_reports_on_course_id"
    t.index ["discarded_at"], name: "index_user_proctor_reports_on_discarded_at"
    t.index ["user_id"], name: "index_user_proctor_reports_on_user_id"
  end

  create_table "user_projects", force: :cascade do |t|
    t.bigint "project_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["project_id"], name: "index_user_projects_on_project_id"
    t.index ["user_id"], name: "index_user_projects_on_user_id"
  end

  create_table "user_questions", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.text "message"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_questions_on_user_id"
  end

  create_table "user_recommended_competencies", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "competency_id", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competency_id"], name: "index_user_recommended_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_user_recommended_competencies_on_discarded_at"
    t.index ["user_id", "competency_id"], name: "index_user_recommended_competencies_on_user_and_competency", unique: true
    t.index ["user_id"], name: "index_user_recommended_competencies_on_user_id"
  end

  create_table "user_remarks", force: :cascade do |t|
    t.text "remark"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "admin_id"
    t.index ["admin_id"], name: "index_user_remarks_on_admin_id"
    t.index ["user_id"], name: "index_user_remarks_on_user_id"
  end

  create_table "user_rewards", force: :cascade do |t|
    t.string "status"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.bigint "course_reward_id"
    t.bigint "external_reward_id"
    t.bigint "promo_code_id"
    t.bigint "vwx_reward_id"
    t.boolean "remind_me_notification", default: false, null: false
    t.datetime "reminder_sent_at", precision: nil
    t.index ["course_reward_id"], name: "index_user_rewards_on_course_reward_id"
    t.index ["discarded_at"], name: "index_user_rewards_on_discarded_at"
    t.index ["external_reward_id"], name: "index_user_rewards_on_external_reward_id"
    t.index ["promo_code_id"], name: "index_user_rewards_on_promo_code_id"
    t.index ["user_id"], name: "index_user_rewards_on_user_id"
    t.index ["vwx_reward_id"], name: "index_user_rewards_on_vwx_reward_id"
  end

  create_table "user_skills", force: :cascade do |t|
    t.enum "level", enum_type: "level"
    t.bigint "skill_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["discarded_at"], name: "index_user_skills_on_discarded_at"
    t.index ["skill_id"], name: "index_user_skills_on_skill_id"
    t.index ["user_id", "skill_id"], name: "index_user_skills_on_user_id_and_skill_id", unique: true
    t.index ["user_id"], name: "index_user_skills_on_user_id"
  end

  create_table "user_specialization_competencies", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "specialization_id", null: false
    t.bigint "competency_id", null: false
    t.float "score", default: 0.0
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competency_id"], name: "index_user_specialization_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_user_specialization_competencies_on_discarded_at"
    t.index ["specialization_id"], name: "index_user_specialization_competencies_on_specialization_id"
    t.index ["user_id", "specialization_id", "competency_id"], name: "index_user_specialization_competencies", unique: true
    t.index ["user_id"], name: "index_user_specialization_competencies_on_user_id"
  end

  create_table "user_sub_competencies", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "sub_competency_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "bloom_taxonomy_max_level_id"
    t.bigint "bloom_taxonomy_min_level_id"
    t.index ["bloom_taxonomy_max_level_id"], name: "index_user_sub_competencies_on_bloom_taxonomy_max_level_id"
    t.index ["bloom_taxonomy_min_level_id"], name: "index_user_sub_competencies_on_bloom_taxonomy_min_level_id"
    t.index ["discarded_at"], name: "index_user_sub_competencies_on_discarded_at"
    t.index ["sub_competency_id"], name: "index_user_sub_competencies_on_sub_competency_id"
    t.index ["user_id", "sub_competency_id"], name: "index_user_sub_competencies_on_user_id_and_sub_competency_id", unique: true
    t.index ["user_id"], name: "index_user_sub_competencies_on_user_id"
  end

  create_table "user_survey_reasons", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "survey_id"
    t.string "reason"
    t.datetime "suspend_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["survey_id"], name: "index_user_survey_reasons_on_survey_id"
    t.index ["user_id"], name: "index_user_survey_reasons_on_user_id"
  end

  create_table "user_surveys", force: :cascade do |t|
    t.bigint "survey_id"
    t.bigint "user_id"
    t.string "state", default: "in_progress"
    t.integer "last_component_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "remind_after", precision: nil
    t.index ["discarded_at"], name: "index_user_surveys_on_discarded_at"
    t.index ["survey_id", "user_id"], name: "index_user_surveys_on_survey_id_and_user_id", unique: true
    t.index ["survey_id"], name: "index_user_surveys_on_survey_id"
    t.index ["user_id"], name: "index_user_surveys_on_user_id"
  end

  create_table "user_taggings", force: :cascade do |t|
    t.float "total_correct_answer_tagging", default: 0.0, null: false
    t.float "total_answered_tagging", default: 0.0, null: false
    t.bigint "tagging_id"
    t.bigint "user_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "score", default: 0.0
    t.index ["discarded_at"], name: "index_user_taggings_on_discarded_at"
    t.index ["tagging_id"], name: "index_user_taggings_on_tagging_id"
    t.index ["user_id", "tagging_id"], name: "index_user_taggings_on_user_id_and_tagging_id", unique: true
    t.index ["user_id"], name: "index_user_taggings_on_user_id"
  end

  create_table "user_tokens", force: :cascade do |t|
    t.string "email"
    t.string "token"
    t.string "token_type"
    t.bigint "user_id"
    t.datetime "token_expired_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "starts_at", precision: nil
    t.string "external_assessment_link"
    t.string "ktp_file_url"
    t.string "selfie_ktp_file_url"
    t.bigint "course_id"
    t.jsonb "config", default: {}, null: false
    t.index ["course_id"], name: "index_user_tokens_on_course_id"
    t.index ["discarded_at"], name: "index_user_tokens_on_discarded_at"
    t.index ["token"], name: "index_user_tokens_on_token", unique: true
    t.index ["token_type"], name: "index_user_tokens_on_token_type"
    t.index ["user_id"], name: "index_user_tokens_on_user_id"
  end

  create_table "user_vacancy_another_source_imports", force: :cascade do |t|
    t.string "file_url"
    t.string "status"
    t.string "error_message"
    t.bigint "admin_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_id"], name: "index_user_vacancy_another_source_imports_on_admin_id"
    t.index ["discarded_at"], name: "index_user_vacancy_another_source_imports_on_discarded_at"
  end

  create_table "user_vacancy_answer_options", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "vacancy_question_option_id", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_vacancy_answer_options_on_discarded_at"
    t.index ["user_id", "vacancy_question_option_id"], name: "index_user_vacancy_answer_options_on_user_id_and_vqo_id", unique: true
    t.index ["user_id"], name: "index_user_vacancy_answer_options_on_user_id"
    t.index ["vacancy_question_option_id"], name: "index_user_vacancy_answer_options_on_vacancy_question_option_id"
  end

  create_table "user_vacancy_batch_updates", force: :cascade do |t|
    t.string "status"
    t.string "pipeline_type"
    t.string "uploaded_file_url"
    t.string "result_file_url"
    t.bigint "admin_id"
    t.integer "total_rows"
    t.boolean "send_email"
    t.integer "user_job_vacancy_ids", default: [], null: false, array: true
    t.datetime "scheduled_at", precision: nil
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "mailer_template_id"
    t.index ["admin_id"], name: "index_user_vacancy_batch_updates_on_admin_id"
    t.index ["discarded_at"], name: "index_user_vacancy_batch_updates_on_discarded_at"
    t.index ["mailer_template_id"], name: "index_user_vacancy_batch_updates_on_mailer_template_id"
  end

  create_table "user_vacancy_matchmaking_results", force: :cascade do |t|
    t.bigint "user_job_vacancy_id", null: false
    t.float "score"
    t.string "status"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "additional_score"
    t.float "main_score"
    t.jsonb "matching_criteria", default: {}
    t.jsonb "additional_values", default: {}
    t.index ["discarded_at"], name: "index_user_vacancy_matchmaking_results_on_discarded_at"
    t.index ["user_job_vacancy_id"], name: "index_user_vacancy_matchmaking_results_on_ujv_id", unique: true
  end

  create_table "user_vacancy_question_answers", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "vacancy_question_id", null: false
    t.string "answer"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_vacancy_question_answers_on_discarded_at"
    t.index ["user_id", "vacancy_question_id"], name: "index_user_vacancy_question_answers_on_user_id_and_vq_id", unique: true
    t.index ["user_id"], name: "index_user_vacancy_question_answers_on_user_id"
    t.index ["vacancy_question_id"], name: "index_user_vacancy_question_answers_on_vacancy_question_id"
  end

  create_table "user_vwx_answers", force: :cascade do |t|
    t.text "answer"
    t.bigint "vwx_essay_id"
    t.bigint "vwx_task_component_id"
    t.bigint "user_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_vwx_answers_on_discarded_at"
    t.index ["user_id"], name: "index_user_vwx_answers_on_user_id"
    t.index ["vwx_essay_id", "vwx_task_component_id", "user_id"], name: "index_user_vwx_answers_essay_id_task_component_id_user_id", unique: true
    t.index ["vwx_essay_id"], name: "index_user_vwx_answers_on_vwx_essay_id"
    t.index ["vwx_task_component_id"], name: "index_user_vwx_answers_on_vwx_task_component_id"
  end

  create_table "user_vwx_partners", force: :cascade do |t|
    t.bigint "user_vwx_id"
    t.bigint "partner_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_user_vwx_partners_on_discarded_at"
    t.index ["partner_id"], name: "index_user_vwx_partners_on_partner_id"
    t.index ["user_vwx_id", "partner_id"], name: "index_user_vwx_partners_on_user_vwx_id_and_partner_id", unique: true
    t.index ["user_vwx_id"], name: "index_user_vwx_partners_on_user_vwx_id"
  end

  create_table "user_vwx_tasks", force: :cascade do |t|
    t.string "state"
    t.bigint "user_id"
    t.bigint "vwx_task_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "completed_at", precision: nil
    t.integer "earned_xp", default: 0
    t.boolean "reminder_sent", default: false
    t.bigint "bundling_code_id"
    t.index ["bundling_code_id"], name: "index_user_vwx_tasks_on_bundling_code_id"
    t.index ["discarded_at"], name: "index_user_vwx_tasks_on_discarded_at"
    t.index ["user_id"], name: "index_user_vwx_tasks_on_user_id"
    t.index ["vwx_task_id", "user_id"], name: "index_user_vwx_tasks_on_vwx_task_id_and_user_id", unique: true
    t.index ["vwx_task_id"], name: "index_user_vwx_tasks_on_vwx_task_id"
  end

  create_table "user_vwxes", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "vwx_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "state", default: "locked"
    t.boolean "due_date_mailer_sent", default: false, null: false
    t.integer "total_xp", default: 0
    t.string "access_tier", default: "free", null: false
    t.float "task_progress", default: 0.0, null: false
    t.string "onboard_state", default: "in_progress"
    t.string "info_sources", default: [], array: true
    t.float "vwx_score", default: 0.0, null: false
    t.string "utm_source"
    t.index ["discarded_at"], name: "index_user_vwxes_on_discarded_at"
    t.index ["state"], name: "index_user_vwxes_on_state"
    t.index ["user_id"], name: "index_user_vwxes_on_user_id"
    t.index ["utm_source"], name: "index_user_vwxes_on_utm_source"
    t.index ["vwx_id", "user_id"], name: "index_user_vwxes_on_vwx_id_and_user_id", unique: true
    t.index ["vwx_id"], name: "index_user_vwxes_on_vwx_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", null: false
    t.string "password_digest", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name", null: false
    t.string "phone_number"
    t.string "location"
    t.string "gender"
    t.string "address"
    t.string "github_url"
    t.string "stackoverflow_url"
    t.string "kaggle_url"
    t.string "behance_url"
    t.string "dribble_url"
    t.string "specialty"
    t.string "calendly_url"
    t.string "gmeet_url"
    t.string "whatsapp_url"
    t.string "npwp_number"
    t.enum "role", default: "student", enum_type: "user_role"
    t.string "job_title"
    t.text "about_me", default: ""
    t.string "linkedin_url"
    t.string "facebook_url"
    t.string "instagram_url"
    t.string "twitter_url"
    t.datetime "discarded_at", precision: nil
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "date_of_birth", precision: nil
    t.enum "status_availability", default: "open_to_work", enum_type: "status_availability"
    t.integer "years_experience", default: 0
    t.string "religion"
    t.string "ethnic"
    t.text "talent_recommendation"
    t.string "portfolio_url"
    t.string "resume_url"
    t.integer "partner_id"
    t.string "address_ktp"
    t.string "place_of_birth"
    t.string "nik"
    t.string "kk"
    t.boolean "agree_assessment_disclaimer", default: false, null: false
    t.string "company_logo_url"
    t.string "signature_url"
    t.integer "total_portfolio", default: 0, null: false
    t.string "level"
    t.bigint "location_id"
    t.jsonb "website_urls", default: []
    t.string "external_id"
    t.string "external_resource"
    t.integer "external_total_enrolled_courses"
    t.integer "rakamin_point", default: 0, null: false
    t.bigint "province_id"
    t.string "vwx_quota_state", default: "available"
    t.boolean "vix_batch_reminder", default: false
    t.bigint "active_career_path_id"
    t.string "profile_picture_url"
    t.string "token"
    t.datetime "token_expired_at", precision: nil
    t.string "verification_state", default: "unverified"
    t.string "verification_method"
    t.boolean "bypass_verification", default: false, null: false
    t.datetime "next_verify_retry_at", precision: nil
    t.integer "resend_counter", default: 0, null: false
    t.string "honesty_statement_url"
    t.string "agreement_provisions_url"
    t.datetime "resume_url_last_updated_at", precision: nil
    t.string "intro_video_url"
    t.string "digital_cv_progress"
    t.string "language"
    t.string "preferred_language", default: "id"
    t.string "division"
    t.string "temp_email"
    t.integer "public_location_id"
    t.index ["active_career_path_id"], name: "index_users_on_active_career_path_id"
    t.index ["discarded_at"], name: "index_users_on_discarded_at"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["external_id", "external_resource"], name: "index_users_on_external_id_and_external_resource", unique: true
    t.index ["kk"], name: "index_users_on_kk", unique: true
    t.index ["location_id"], name: "index_users_on_location_id"
    t.index ["nik"], name: "index_users_on_nik", unique: true
    t.index ["province_id"], name: "index_users_on_province_id"
    t.index ["public_location_id"], name: "index_users_on_public_location_id"
    t.index ["temp_email"], name: "index_users_on_temp_email", unique: true
    t.index ["token"], name: "index_users_on_token", unique: true
    t.index ["vwx_quota_state"], name: "index_users_on_vwx_quota_state"
  end

  create_table "vacancy_question_options", force: :cascade do |t|
    t.bigint "vacancy_question_id", null: false
    t.string "option"
    t.integer "order_level", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_vacancy_question_options_on_discarded_at"
    t.index ["vacancy_question_id"], name: "index_vacancy_question_options_on_vacancy_question_id"
  end

  create_table "vacancy_questions", force: :cascade do |t|
    t.bigint "job_vacancy_id"
    t.string "question"
    t.string "question_type"
    t.integer "order_level", null: false
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "config", default: {}, null: false
    t.index ["discarded_at"], name: "index_vacancy_questions_on_discarded_at"
    t.index ["job_vacancy_id"], name: "index_vacancy_questions_on_job_vacancy_id"
  end

  create_table "visits", force: :cascade do |t|
    t.integer "user_id"
    t.bigint "hour_timestamp"
    t.datetime "time", precision: nil
    t.index ["user_id", "hour_timestamp"], name: "index_visits_on_user_id_and_hour_timestamp", unique: true
  end

  create_table "vwx_competencies", force: :cascade do |t|
    t.bigint "vwx_id"
    t.bigint "competency_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competency_id"], name: "index_vwx_competencies_on_competency_id"
    t.index ["discarded_at"], name: "index_vwx_competencies_on_discarded_at"
    t.index ["vwx_id", "competency_id"], name: "index_vwx_competencies_on_vwx_id_and_competency_id", unique: true
    t.index ["vwx_id"], name: "index_vwx_competencies_on_vwx_id"
  end

  create_table "vwx_duplicates", force: :cascade do |t|
    t.string "status", null: false
    t.jsonb "duplicate_ids", default: [], null: false
    t.jsonb "duplicated_ids", default: [], null: false
    t.string "error_message"
    t.jsonb "update_params", default: {}, null: false
    t.bigint "user_id", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_vwx_duplicates_on_discarded_at"
    t.index ["status"], name: "index_vwx_duplicates_on_status"
    t.index ["user_id"], name: "index_vwx_duplicates_on_user_id"
  end

  create_table "vwx_essays", force: :cascade do |t|
    t.text "question"
    t.string "answer_keys", array: true
    t.integer "order_level", default: 0, null: false
    t.text "note"
    t.string "essay_type"
    t.bigint "vwx_task_component_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "input_type"
    t.index ["discarded_at"], name: "index_vwx_essays_on_discarded_at"
    t.index ["vwx_task_component_id"], name: "index_vwx_essays_on_vwx_task_component_id"
  end

  create_table "vwx_industry_categories", force: :cascade do |t|
    t.bigint "vwx_id"
    t.bigint "industry_category_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_vwx_industry_categories_on_discarded_at"
    t.index ["industry_category_id"], name: "index_vwx_industry_categories_on_industry_category_id"
    t.index ["vwx_id", "industry_category_id"], name: "index_vwx_industry_categories_vwx_id_industry_id", unique: true
    t.index ["vwx_id"], name: "index_vwx_industry_categories_on_vwx_id"
  end

  create_table "vwx_job_roles", force: :cascade do |t|
    t.bigint "vwx_id"
    t.bigint "job_role_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_vwx_job_roles_on_discarded_at"
    t.index ["job_role_id"], name: "index_vwx_job_roles_on_job_role_id"
    t.index ["vwx_id", "job_role_id"], name: "index_vwx_job_roles_on_vwx_id_and_job_role_id", unique: true
    t.index ["vwx_id"], name: "index_vwx_job_roles_on_vwx_id"
  end

  create_table "vwx_reminders", force: :cascade do |t|
    t.string "email", null: false
    t.datetime "batch", precision: nil, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "discarded_at", precision: nil
    t.index ["batch"], name: "index_vwx_reminders_on_batch"
    t.index ["discarded_at"], name: "index_vwx_reminders_on_discarded_at"
    t.index ["email", "batch"], name: "index_vwx_reminders_on_email_and_batch", unique: true
  end

  create_table "vwx_request_competencies", force: :cascade do |t|
    t.bigint "vwx_request_id"
    t.bigint "competency_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["competency_id"], name: "index_vwx_request_competencies_on_competency_id"
    t.index ["discarded_at"], name: "vwx_request_competencies_discarded_at_index"
    t.index ["vwx_request_id", "competency_id"], name: "vwx_request_competencies_fk_index", unique: true
    t.index ["vwx_request_id"], name: "index_vwx_request_competencies_on_vwx_request_id"
  end

  create_table "vwx_requests", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "industry_category_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "partner_id"
    t.bigint "job_role_id"
    t.text "job_description"
    t.string "partner_pic_name"
    t.string "partner_pic_position"
    t.string "partner_pic_contact"
    t.string "file_url"
    t.index ["discarded_at"], name: "index_vwx_requests_on_discarded_at"
    t.index ["industry_category_id"], name: "index_vwx_requests_on_industry_category_id"
    t.index ["job_role_id"], name: "index_vwx_requests_on_job_role_id"
    t.index ["partner_id"], name: "index_vwx_requests_on_partner_id"
    t.index ["user_id"], name: "index_vwx_requests_on_user_id"
  end

  create_table "vwx_resources", force: :cascade do |t|
    t.string "title"
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.string "url"
    t.string "resource_type"
    t.bigint "vwx_task_component_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "description"
    t.index ["discarded_at"], name: "index_vwx_resources_on_discarded_at"
    t.index ["vwx_task_component_id"], name: "index_vwx_resources_on_vwx_task_component_id"
  end

  create_table "vwx_rewards", force: :cascade do |t|
    t.integer "exp_required"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "vwx_id", null: false
    t.bigint "reward_id", null: false
    t.index ["discarded_at"], name: "index_vwx_rewards_on_discarded_at"
    t.index ["reward_id"], name: "index_vwx_rewards_on_reward_id"
    t.index ["vwx_id"], name: "index_vwx_rewards_on_vwx_id"
  end

  create_table "vwx_sections", force: :cascade do |t|
    t.string "name"
    t.bigint "vwx_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "order_level", default: 0, null: false
    t.text "completed_message"
    t.index ["discarded_at"], name: "index_vwx_sections_on_discarded_at"
    t.index ["vwx_id"], name: "index_vwx_sections_on_vwx_id"
  end

  create_table "vwx_skills", force: :cascade do |t|
    t.bigint "vwx_id"
    t.bigint "skill_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "assigned_difficulties", default: [], array: true
    t.index ["discarded_at"], name: "index_vwx_skills_on_discarded_at"
    t.index ["skill_id"], name: "index_vwx_skills_on_skill_id"
    t.index ["vwx_id", "skill_id"], name: "index_vwx_skills_on_vwx_id_and_skill_id", unique: true
    t.index ["vwx_id"], name: "index_vwx_skills_on_vwx_id"
  end

  create_table "vwx_task_components", force: :cascade do |t|
    t.string "task_component_type"
    t.string "task_result_url"
    t.integer "order_level", default: 0, null: false
    t.string "title"
    t.text "description"
    t.string "video_url"
    t.bigint "vwx_task_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "video_thumbnail_url"
    t.string "video_source", default: "s3", null: false
    t.boolean "recalculation_active_status", default: false
    t.datetime "last_recalculation_at", precision: nil
    t.string "comparison_document_url"
    t.index ["discarded_at"], name: "index_vwx_task_components_on_discarded_at"
    t.index ["vwx_task_id"], name: "index_vwx_task_components_on_vwx_task_id"
  end

  create_table "vwx_tasks", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.text "benefits", array: true
    t.integer "order_level", default: 0, null: false
    t.string "difficulty"
    t.bigint "vwx_section_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "image_url"
    t.datetime "due_date", precision: nil
    t.integer "reward_xp", default: 0
    t.integer "duration", default: 0
    t.string "task_type", default: "task", null: false
    t.bigint "tutor_id"
    t.index ["discarded_at"], name: "index_vwx_tasks_on_discarded_at"
    t.index ["order_level", "vwx_section_id"], name: "unique_index_vwx_tasks_order_level_vwx_section_id", unique: true
    t.index ["tutor_id"], name: "index_vwx_tasks_on_tutor_id"
    t.index ["vwx_section_id"], name: "index_vwx_tasks_on_vwx_section_id"
  end

  create_table "vwxes", force: :cascade do |t|
    t.string "title"
    t.string "video_url"
    t.text "description"
    t.string "difficulty"
    t.bigint "job_role_id"
    t.bigint "partner_id"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "external_id"
    t.datetime "starts_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.string "vwx_type", default: "virtual_working_experience"
    t.integer "tutor_id"
    t.integer "quota", default: 200, null: false
    t.datetime "registration_starts_at", precision: nil
    t.datetime "registration_ends_at", precision: nil
    t.datetime "announcement_date", precision: nil
    t.string "visibility", default: "public"
    t.integer "opening_xp", default: 0
    t.integer "duration", default: 0
    t.string "vwx_state", default: "published"
    t.bigint "archive_source_id"
    t.float "price", default: 0.0, null: false
    t.integer "discount_percentage", default: 0, null: false
    t.string "features", default: [], array: true
    t.boolean "monetization", default: false, null: false
    t.string "video_thumbnail_url"
    t.string "video_source", default: "s3", null: false
    t.integer "selected_certificate_skill_ids", default: [], null: false, array: true
    t.jsonb "config", default: {}
    t.index ["archive_source_id"], name: "index_vwxes_on_archive_source_id"
    t.index ["discarded_at"], name: "index_vwxes_on_discarded_at"
    t.index ["external_id"], name: "index_vwxes_on_external_id"
    t.index ["job_role_id"], name: "index_vwxes_on_job_role_id"
    t.index ["partner_id"], name: "index_vwxes_on_partner_id"
    t.index ["tutor_id"], name: "index_vwxes_on_tutor_id"
    t.index ["visibility"], name: "index_vwxes_on_visibility"
    t.index ["vwx_type"], name: "index_vwxes_on_vwx_type"
  end

  create_table "web_configs", force: :cascade do |t|
    t.string "key"
    t.jsonb "value"
    t.datetime "discarded_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "group", default: "assessment", null: false
    t.index ["key"], name: "index_web_configs_on_key", unique: true
    t.index ["value"], name: "index_web_configs_on_value", using: :gin
  end

  create_table "web_domains", force: :cascade do |t|
    t.string "host", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["host"], name: "index_web_domains_on_host", unique: true
  end

  create_table "wishlists", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "course_id"
    t.enum "state", default: "active", enum_type: "wishlist_state"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_id"], name: "index_wishlists_on_course_id"
    t.index ["user_id"], name: "index_wishlists_on_user_id"
  end

  add_foreign_key "achievement_competencies", "achievements"
  add_foreign_key "achievement_competencies", "competencies"
  add_foreign_key "achievements", "courses"
  add_foreign_key "achievements", "educations"
  add_foreign_key "achievements", "licenses"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "ai_assistant_runs", "ai_assistants"
  add_foreign_key "ai_assistant_runs", "users"
  add_foreign_key "ai_chats", "ai_prompts"
  add_foreign_key "ai_chats", "users"
  add_foreign_key "ai_contexts", "ai_prompts"
  add_foreign_key "ai_messages", "ai_chats"
  add_foreign_key "assessment_items", "bloom_taxonomies"
  add_foreign_key "assessment_items", "job_roles"
  add_foreign_key "assessment_items", "specializations"
  add_foreign_key "assessment_items", "sub_competencies"
  add_foreign_key "assessment_sub_scores", "courses"
  add_foreign_key "assessment_sub_scores", "users"
  add_foreign_key "assessment_sub_type_facets", "assessment_sub_types"
  add_foreign_key "assignment_scores", "final_scoring_metrics"
  add_foreign_key "assignment_scores", "final_scoring_sub_metrics"
  add_foreign_key "assignment_scores", "final_scoring_verificators"
  add_foreign_key "assignment_scores", "users", column: "feedback_by_id"
  add_foreign_key "assignment_scores", "vwx_task_components"
  add_foreign_key "bundling_codes", "module_sessions"
  add_foreign_key "cc_assessment_sub_types", "assessment_sub_types"
  add_foreign_key "cc_assessment_sub_types", "course_configs"
  add_foreign_key "cc_ast_facets", "assessment_sub_type_facets"
  add_foreign_key "cc_ast_facets", "cc_assessment_sub_types"
  add_foreign_key "certificates", "courses"
  add_foreign_key "company_assessment_templates", "courses"
  add_foreign_key "company_assessment_templates", "job_role_groups"
  add_foreign_key "company_assessment_templates", "module_sessions"
  add_foreign_key "company_assessment_templates", "partners"
  add_foreign_key "competencies", "bloom_taxonomies", column: "bloom_taxonomy_max_level_id"
  add_foreign_key "competencies", "bloom_taxonomies", column: "bootcamp_level_id"
  add_foreign_key "competencies", "bloom_taxonomies", column: "vix_level_id"
  add_foreign_key "competencies", "job_roles"
  add_foreign_key "course_reviews", "courses"
  add_foreign_key "course_rewards", "courses"
  add_foreign_key "course_rewards", "rewards"
  add_foreign_key "courses", "majors"
  add_foreign_key "courses", "universities"
  add_foreign_key "educations", "education_levels"
  add_foreign_key "educations", "universities"
  add_foreign_key "experience_competencies", "competencies"
  add_foreign_key "experience_competencies", "experiences"
  add_foreign_key "experience_industry_categories", "experiences"
  add_foreign_key "experience_industry_categories", "industry_categories"
  add_foreign_key "experiences", "experiences", column: "promoted_from_id"
  add_foreign_key "experiences", "job_levels"
  add_foreign_key "experiences", "job_roles"
  add_foreign_key "experiences", "partner_placements"
  add_foreign_key "experiences", "partners"
  add_foreign_key "experiences", "vwxes"
  add_foreign_key "feature_permissions", "users"
  add_foreign_key "final_scoring_sub_metrics", "courses"
  add_foreign_key "hiring_pipeline_column_filters", "hiring_pipeline_column_settings"
  add_foreign_key "hiring_processes", "user_job_vacancies"
  add_foreign_key "homework_question_taggings", "taggings"
  add_foreign_key "homework_questions", "assessment_items"
  add_foreign_key "homework_questions", "bundling_codes"
  add_foreign_key "homework_questions", "stimuli", column: "stimulus_id"
  add_foreign_key "job_levels", "partners"
  add_foreign_key "invite_assesses", "users", column: "admin_id"
  add_foreign_key "job_vacancies", "education_levels"
  add_foreign_key "job_vacancies", "industry_categories"
  add_foreign_key "job_vacancies", "job_role_groups"
  add_foreign_key "job_vacancies", "partner_placements"
  add_foreign_key "job_vacancy_assessments", "company_assessment_templates"
  add_foreign_key "job_vacancy_assessments", "job_vacancies"
  add_foreign_key "job_vacancy_competencies", "competencies"
  add_foreign_key "job_vacancy_competencies", "job_vacancies"
  add_foreign_key "job_vacancy_licenses", "job_vacancies"
  add_foreign_key "job_vacancy_licenses", "licenses"
  add_foreign_key "job_vacancy_recruiters", "job_vacancies"
  add_foreign_key "job_vacancy_recruiters", "users", column: "recruiter_id"
  add_foreign_key "job_vacancy_section_questions", "job_vacancy_sections"
  add_foreign_key "job_vacancy_section_questions", "vacancy_questions"
  add_foreign_key "job_vacancy_sections", "job_vacancies"
  add_foreign_key "job_vacancy_taggings", "job_vacancies"
  add_foreign_key "job_vacancy_university_majors", "job_vacancies"
  add_foreign_key "leadership_experiences", "users"
  add_foreign_key "mailer_templates", "partners"
  add_foreign_key "mailer_templates", "qiscus_message_templates"
  add_foreign_key "mailer_templates", "users", column: "latest_update_admin_id"
  add_foreign_key "matchmaking_queues", "job_vacancies"
  add_foreign_key "mentoring_groups", "users", column: "assistant_tutor_id"
  add_foreign_key "module_sessions", "vwx_task_components"
  add_foreign_key "partner_competencies", "partners"
  add_foreign_key "organization_experiences", "users"
  add_foreign_key "partner_attributes", "partners"
  add_foreign_key "partner_placements", "partners"
  add_foreign_key "partner_saved_talents", "partners"
  add_foreign_key "partner_saved_talents", "users"
  add_foreign_key "permission_groups", "groups"
  add_foreign_key "portfolio_competencies", "competencies"
  add_foreign_key "portfolio_competencies", "portfolios"
  add_foreign_key "portfolios", "vwxes"
  add_foreign_key "prakerja_statuses", "courses"
  add_foreign_key "prakerja_statuses", "promo_codes"
  add_foreign_key "prakerja_statuses", "users"
  add_foreign_key "program_registrations", "education_levels"
  add_foreign_key "program_registrations", "provinces"
  add_foreign_key "program_registrations", "universities"
  add_foreign_key "promo_codes", "courses"
  add_foreign_key "promo_codes", "job_guarantees"
  add_foreign_key "promo_codes", "users"
  add_foreign_key "qiscus_messages", "qiscus_message_templates"
  add_foreign_key "qiscus_messages", "users"
  add_foreign_key "report_assessments", "user_job_vacancy_assessments"
  add_foreign_key "specializations", "career_paths"
  add_foreign_key "specializations", "job_roles"
  add_foreign_key "sub_competencies", "bloom_taxonomies", column: "bloom_taxonomy_max_level_id"
  add_foreign_key "sub_competency_specializations", "bloom_taxonomies", column: "max_level_bloom_taxonomy_id"
  add_foreign_key "sub_competency_specializations", "specializations"
  add_foreign_key "sub_competency_specializations", "sub_competencies"
  add_foreign_key "talent_data_crawling_requests", "users", column: "request_by_id"
  add_foreign_key "talent_data_crawlings", "talent_data_crawling_requests", column: "request_id"
  add_foreign_key "talent_data_crawlings", "users"
  add_foreign_key "talent_recommendations", "job_role_groups"
  add_foreign_key "talent_recommendations", "users"
  add_foreign_key "talent_recommendations", "users", column: "recommended_by_id"
  add_foreign_key "ti_analytic_comparisons", "partner_attributes", column: "variable_id"
  add_foreign_key "ti_analytic_projects", "partner_attributes", column: "x_variable_id"
  add_foreign_key "ti_analytic_projects", "partner_attributes", column: "y_variable_id"
  add_foreign_key "ti_analytic_projects", "partners"
  add_foreign_key "ti_idp_project_users", "ti_idp_projects"
  add_foreign_key "ti_idp_project_users", "users"
  add_foreign_key "ti_idp_projects", "partners"
  add_foreign_key "universities", "provinces"
  add_foreign_key "user_aggregates", "users"
  add_foreign_key "user_assessments", "users"
  add_foreign_key "user_change_emails", "users"
  add_foreign_key "user_competencies", "bloom_taxonomies", column: "bloom_taxonomy_max_level_id"
  add_foreign_key "user_competencies", "bloom_taxonomies", column: "bloom_taxonomy_min_level_id"
  add_foreign_key "user_course_counselings", "user_courses"
  add_foreign_key "user_course_counselings", "users", column: "counselor_id"
  add_foreign_key "user_course_reviews", "courses"
  add_foreign_key "user_course_reviews", "users"
  add_foreign_key "user_courses", "job_guarantees"
  add_foreign_key "user_digital_cv_anonymous_visitors", "users", column: "visited_user_id"
  add_foreign_key "user_digital_cv_settings", "users"
  add_foreign_key "user_digital_cv_visitors", "users", column: "visited_user_id"
  add_foreign_key "user_digital_cv_visitors", "users", column: "visitor_user_id"
  add_foreign_key "user_digital_cvs", "users"
  add_foreign_key "user_documents", "users", column: "admin_id"
  add_foreign_key "user_events", "courses"
  add_foreign_key "user_events", "job_guarantees"
  add_foreign_key "user_feature_onboardings", "features"
  add_foreign_key "user_feature_onboardings", "users"
  add_foreign_key "user_feedbacks", "feedback_templates"
  add_foreign_key "user_feedbacks", "users"
  add_foreign_key "user_gcalendar_events", "gcalendar_events"
  add_foreign_key "user_gcalendar_events", "users"
  add_foreign_key "user_groups", "groups"
  add_foreign_key "user_groups", "users"
  add_foreign_key "user_hiring_pipeline_feedbacks", "user_hiring_pipelines"
  add_foreign_key "user_hiring_pipeline_feedbacks", "users"
  add_foreign_key "user_hiring_pipeline_submissions", "user_hiring_pipelines"
  add_foreign_key "user_import_results", "users"
  add_foreign_key "user_installments", "installment_requests"
  add_foreign_key "user_installments", "promo_codes", column: "jgp_promo_id"
  add_foreign_key "user_invoices", "installment_requests"
  add_foreign_key "user_invoices", "job_guarantees"
  add_foreign_key "user_job_role_groups", "job_role_groups"
  add_foreign_key "user_job_role_groups", "users"
  add_foreign_key "user_job_vacancies", "job_vacancy_taggings"
  add_foreign_key "user_job_vacancies", "partner_placements"
  add_foreign_key "user_job_vacancies", "users", column: "hr_interview_by_id"
  add_foreign_key "user_job_vacancies", "users", column: "invited_by_id"
  add_foreign_key "user_job_vacancies", "users", column: "offering_by_id"
  add_foreign_key "user_job_vacancies", "users", column: "rejected_by_id"
  add_foreign_key "user_job_vacancies", "users", column: "selected_by_id"
  add_foreign_key "user_job_vacancies", "users", column: "technical_pretest_by_id"
  add_foreign_key "user_job_vacancies", "users", column: "user_interview_by_id"
  add_foreign_key "user_job_vacancy_assessments", "job_vacancy_assessments"
  add_foreign_key "user_job_vacancy_assessments", "user_assessments"
  add_foreign_key "user_job_vacancy_assessments", "user_tokens"
  add_foreign_key "user_job_vacancy_assessments", "users"
  add_foreign_key "user_module_sessions", "bundling_codes"
  add_foreign_key "user_module_sessions", "homework_questions", column: "last_visited_question_id"
  add_foreign_key "user_notifications", "users"
  add_foreign_key "user_partner_competencies", "partner_competencies"
  add_foreign_key "user_partner_competencies", "users"
  add_foreign_key "user_partner_competency_histories", "user_partner_competencies"
  add_foreign_key "user_personalities", "users"
  add_foreign_key "user_partner_attributes", "partner_attributes"
  add_foreign_key "user_partner_attributes", "users"
  add_foreign_key "user_preferences", "users"
  add_foreign_key "user_proctor_logs", "courses"
  add_foreign_key "user_proctor_logs", "users"
  add_foreign_key "user_proctor_reports", "courses"
  add_foreign_key "user_proctor_reports", "users"
  add_foreign_key "user_recommended_competencies", "competencies"
  add_foreign_key "user_recommended_competencies", "users"
  add_foreign_key "user_remarks", "users", column: "admin_id"
  add_foreign_key "user_rewards", "external_rewards"
  add_foreign_key "user_rewards", "promo_codes"
  add_foreign_key "user_specialization_competencies", "competencies"
  add_foreign_key "user_specialization_competencies", "specializations"
  add_foreign_key "user_specialization_competencies", "users"
  add_foreign_key "user_sub_competencies", "bloom_taxonomies", column: "bloom_taxonomy_max_level_id"
  add_foreign_key "user_sub_competencies", "bloom_taxonomies", column: "bloom_taxonomy_min_level_id"
  add_foreign_key "user_tokens", "courses"
  add_foreign_key "user_vacancy_another_source_imports", "users", column: "admin_id"
  add_foreign_key "user_vacancy_answer_options", "users"
  add_foreign_key "user_vacancy_answer_options", "vacancy_question_options"
  add_foreign_key "user_vacancy_batch_updates", "mailer_templates"
  add_foreign_key "user_vacancy_batch_updates", "users", column: "admin_id"
  add_foreign_key "user_vacancy_question_answers", "users"
  add_foreign_key "user_vacancy_question_answers", "vacancy_questions"
  add_foreign_key "user_vwx_tasks", "bundling_codes"
  add_foreign_key "users", "provinces"
  add_foreign_key "users", "user_career_paths", column: "active_career_path_id"
  add_foreign_key "vacancy_question_options", "vacancy_questions"
  add_foreign_key "vacancy_questions", "job_vacancies"
  add_foreign_key "vwx_duplicates", "users"
  add_foreign_key "vwx_requests", "job_roles"
  add_foreign_key "vwx_requests", "partners"
  add_foreign_key "vwx_rewards", "rewards"
  add_foreign_key "vwx_rewards", "vwxes"
  add_foreign_key "vwx_tasks", "users", column: "tutor_id"
  add_foreign_key "vwxes", "vwxes", column: "archive_source_id"
end
