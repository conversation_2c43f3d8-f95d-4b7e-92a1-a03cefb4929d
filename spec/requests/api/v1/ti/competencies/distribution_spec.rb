# frozen_string_literal: true

require 'rails_helper'

RSpec.xdescribe 'Api::V1::Ti::CompetenciesController#distribution', type: :request do
  let(:partner) { create(:partner) }
  let(:admin) { create(:user, :admin, partner_id: partner.id) }
  let(:student) { create(:user) }

  def distributions(params = {}, which_user = admin)
    get '/api/v1/ti/competencies/distributions', params, as_user(which_user)
  end

  def overall(params = {}, which_user = admin)
    get '/api/v1/ti/competencies/overall', params, as_user(which_user)
  end

  def users(params = {}, which_user = admin)
    get '/api/v1/ti/competencies/users', params, as_user(which_user)
  end

  let(:distribution_params) do
    {
      group_by: 'competency_category'
    }
  end

  let(:overall_params) do
    {
      overall_competencies_achievement: true
    }
  end

  describe 'GET #distributions' do
    it 'returns data that group by competency_name' do
      distributions(distribution_params)

      expect_response :ok
      expect(response_data.size > 1).to be_truthy

      response_data.each do |data|
        expect(data['label']).to be_truthy
        expect(data['avg_total_comp_score']).to be_truthy
      end
    end

    it 'returns data that group by division' do
      distributions(distribution_params.merge(group_by: 'division'))

      expect_response :ok
      expect(response_data.size > 1).to be_truthy

      response_data.each do |data|
        expect(data['label']).to be_truthy
        expect(data['avg_total_comp_score']).to be_truthy
      end
    end

    it 'returns data that group by job_level' do
      distributions(distribution_params.merge(group_by: 'job_level_id'))

      expect_response :ok
      expect(response_data.size > 1).to be_truthy

      response_data.each do |data|
        expect(data['label']).to be_truthy
        expect(data['avg_total_comp_score']).to be_truthy
      end
    end

    context 'when gap parameter is sent' do
      it 'returns a successful response' do
        distributions(distribution_params.merge(gap: true))

        expect_response :ok
        expect(response_data.size > 1).to be_truthy

        response_data.each do |data|
          expect(data['label']).to be_truthy
          expect(data['avg_total_comp_score']).to be_truthy
        end
      end

      it 'returns data that group by division' do
        distributions(distribution_params.merge(gap: true, group_by: 'division'))

        expect_response :ok
        expect(response_data.size > 1).to be_truthy

        response_data.each do |data|
          expect(data['label']).to be_truthy
          expect(data['avg_total_comp_score']).to be_truthy
        end
      end

      it 'returns data that group by job_level' do
        distributions(distribution_params.merge(gap: true, group_by: 'job_level_id'))

        expect_response :ok
        expect(response_data.size > 1).to be_truthy

        response_data.each do |data|
          expect(data['label']).to be_truthy
          expect(data['avg_total_comp_score']).to be_truthy
        end
      end
    end
  end

  describe 'GET #overall' do
    it 'returns a successful response on overall_competencies_achievement params sent' do
      overall({ overall_competencies_achievement: true })
      expect_response :ok
      expect(response_data.size).to eq(1)
    end

    it 'returns a successful response on leaderboard_competencies params sent' do
      overall({ leaderboard_competencies: true })

      expect_response :ok
      expect(response_data.size).to eq(6)

      object_response = response_data.first
      expect(object_response.keys.size > 1).to be_truthy
    end

    context 'when gap parameter is sent' do
      it 'returns a successful response' do
        overall({ gap: true, overall_competencies_achievement: true })

        expect_response :ok
        expect(response_data.size).to eq(1)

        object_response = response_data.first
        expect(object_response.keys.size >= 1).to be_truthy
      end

      it 'returns a successful response on leaderboard_competencies params sent' do
        overall({ leaderboard_competencies: true, gap: true })

        expect_response :ok
        expect(response_data.size).to eq(6)

        object_response = response_data.first
        expect(object_response.keys.size > 1).to be_truthy
      end
    end
  end

  describe 'GET #users' do
    it 'returns a successful response' do
      users

      expect_response :ok
      expect(response_data.size).to eq(40)

      response_data.each do |data|
        expect(data['name']).to be_truthy
        expect(data['employee_id']).to be_truthy
        expect(data['user_id']).to be_truthy
        expect(data['role_name']).to be_truthy
        expect(data['division']).to be_truthy
      end
    end

    context 'when gap parameter is sent' do
      it 'returns a successful response' do
        users({ gap: true })

        expect_response :ok
        expect(response_data.size).to eq(40)

        response_data.each do |data|
          expect(data['name']).to be_truthy
          expect(data['employee_id']).to be_truthy
          expect(data['user_id']).to be_truthy
          expect(data['role_name']).to be_truthy
          expect(data['division']).to be_truthy
        end
      end
    end
  end

  describe 'GET #heatmap' do
    it 'returns a successful response' do
      get '/api/v1/ti/competencies/heatmap', {}, as_user(admin)

      expect_response :ok

      response_data.each do |data|
        expect(data['job_level_name']).to be_truthy
        expect(data['division']).to be_truthy
        expect(data.key?('average')).to be_truthy
      end
    end

    context 'when gap parameter is sent' do
      it 'returns a successful response' do
        get '/api/v1/ti/competencies/heatmap', { gap: true }, as_user(admin)

        expect_response :ok

        response_data.each do |data|
          expect(data['job_level_name']).to be_truthy
          expect(data['division']).to be_truthy
          expect(data.key?('average')).to be_truthy
        end
      end
    end
  end
end
