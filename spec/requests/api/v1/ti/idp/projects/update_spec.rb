# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Ti::IdpProjects#update', type: :request do
  let(:partner) { create(:partner) }
  let(:other_partner) { create(:partner) }
  let(:admin) { create(:user, :admin, partner_id: partner.id) }
  let(:other_admin) { create(:user, :admin, partner_id: other_partner.id) }
  let(:student) { create(:user) }
  let!(:project) { create(:ti_idp_project, partner: partner, name: 'Old Name') }
  let!(:other_project) { create(:ti_idp_project, partner: other_partner) }

  let(:valid_params) do
    {
      name: 'New Project Name'
    }
  end

  def update_project(project_id, params, which_user = admin)
    put "/api/v1/ti_idp/projects/#{project_id}", params, as_user(which_user)
  end

  context 'when user is partner admin' do
    it 'updates the specified IDP project' do
      update_project(project.id, valid_params)
      expect_response :ok

      project.reload
      expect(project.name).to eq 'New Project Name'
      expect(response_data['id']).to eq project.id
      expect(response_data['name']).to eq 'New Project Name'
    end

    it 'returns not found if the project does not exist' do
      update_project(0, valid_params)
      expect_response :not_found
    end

    it 'returns not found if the project belongs to a different partner' do
      update_project(other_project.id, valid_params)
      expect_response :not_found
    end
  end

  context 'when user is not partner admin' do
    it 'returns unauthorized' do
      update_project(project.id, valid_params, student)
      expect_response(:forbidden)
    end
  end
end