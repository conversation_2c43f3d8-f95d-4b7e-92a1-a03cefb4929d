# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Ti::IdpProjects#create', type: :request do
  let(:partner) { create(:partner) }
  let(:other_partner) { create(:partner) }
  let(:admin) { create(:user, :admin, partner_id: partner.id) }
  let(:student1) { create(:user, partner_id: partner.id) }
  let(:student2) { create(:user, partner_id: partner.id) }
  let(:other_student) { create(:user, partner_id: other_partner.id) }

  let(:valid_params) do
    {
      name: 'New IDP Project',
      user_ids: [student1.id, student2.id]
    }
  end

  let(:valid_params_without_name) do
    {
      user_ids: [student1.id]
    }
  end

  def create_project(params, which_user = admin)
    post '/api/v1/ti_idp/projects', params, as_user(which_user)
  end

  context 'when user is partner admin' do
    before { Sidekiq::Testing.fake! }

    it 'creates a new IDP project with name and users' do
      create_project(valid_params)
      expect_response :created
      expect(response_data['name']).to eq 'New IDP Project'

      response_id = response_data['id']
      project = ::Ti::IdpProject.find(response_id)
      user_ids = project.users.pluck(:id)
      expect(user_ids).to match_array([student1.id, student2.id])
      expect(project.status).to eq 'queued'
    end

    it 'creates a new IDP project without a name' do
      create_project(valid_params_without_name)
      expect_response :created
      expect(response_data['name']).to be_nil

      response_id = response_data['id']
      project = ::Ti::IdpProject.find(response_id)
      user_ids = project.users.pluck(:id)
      expect(user_ids).to match_array([student1.id])
    end

    it 'returns validation error when user_ids is empty' do
      invalid_params = valid_params.merge(user_ids: [])
      create_project(invalid_params)
      expect_error_response(422, "User ID(s) can't be blank.")
    end

    it 'returns validation error when user_ids contains invalid user ids' do
      invalid_params = valid_params.merge(user_ids: [student1.id, 0])
      create_project(invalid_params)

      expect_error_response(
        422,
        <<~MSG.squish
          Invalid User ID(s).
          Please ensure all provided User IDs exist
          and belong to the same partner_id.
        MSG
      )
    end

    it 'returns validation error when user_ids contains user ids from a different partner' do
      invalid_params = valid_params.merge(user_ids: [student1.id, other_student.id])
      create_project(invalid_params)

      expect_error_response(
        422,
        <<~MSG.squish
          Invalid User ID(s).
          Please ensure all provided User IDs exist
          and belong to the same partner_id.
        MSG
      )
    end
  end

  context 'when user is not partner admin' do
    it 'returns unauthorized' do
      create_project(valid_params, other_student)
      expect_response(:forbidden)
    end
  end
end