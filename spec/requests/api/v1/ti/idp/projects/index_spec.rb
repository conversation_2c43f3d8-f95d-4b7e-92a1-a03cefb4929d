# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Ti::IdpProjects#index', type: :request do
  let(:partner) { create(:partner) }
  let(:other_partner) { create(:partner) }
  let(:admin) { create(:user, :admin, partner_id: partner.id) }
  let(:other_admin) { create(:user, :admin, partner_id: other_partner.id) }
  let(:student) { create(:user) }

  let!(:project1) { create(:ti_idp_project, partner: admin.partner) }
  let!(:project2) { create(:ti_idp_project, partner: admin.partner) }
  let!(:other_project) { create(:ti_idp_project, partner: other_partner) }

  def list_projects(which_user = admin)
    get '/api/v1/ti_idp/projects', {}, as_user(which_user)
  end

  context 'when user is partner admin' do
    before do
      ::Ti::IdpProjectUser.create!(
        user: student,
        project: project1
      )

      project1.update!(created_at: Time.new(2025, 1, 1, 1, 0, 0, 'utc'))
      project2.update!(created_at: Time.new(2025, 2, 1, 1, 0, 0, 'utc'))
    end

    it 'returns a list of IDP projects for their partner' do
      list_projects
      expect_response :ok
      expect(response_data.size).to eq 2

      expected_ids = [project1.id, project2.id]
      actual_ids = response_data.pluck('id')
      expect(actual_ids).to match_array(expected_ids)

      project1_res = response_data.find { _1['id'] == project1.id }
      expect(project1_res['users_count']).to eq 1
      expect(project1_res['created_at']).to eq '2025-01-01T01:00:00.000Z'

      project2_res = response_data.find { _1['id'] == project2.id }
      expect(project2_res['users_count']).to be_zero
      expect(project2_res['created_at']).to eq '2025-02-01T01:00:00.000Z'

      list_projects(other_admin)
      expect_response :ok
      expect(response_data.size).to eq 1

      expected_ids = [other_project.id]
      actual_ids = response_data.pluck('id')
      expect(actual_ids).to match_array(expected_ids)

      other_project_res = response_data.first
      expect(other_project_res['users_count']).to be_zero
    end
  end

  context 'when user is not partner admin' do
    it 'returns unauthorized' do
      list_projects(student)
      expect_response(:forbidden)
    end
  end
end