# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Ti::IdpProjects#destroy', type: :request do
  let(:partner) { create(:partner) }
  let(:other_partner) { create(:partner) }
  let(:admin) { create(:user, :admin, partner_id: partner.id) }
  let(:student) { create(:user) }
  let!(:project) { create(:ti_idp_project, partner: admin.partner) }
  let!(:other_project) { create(:ti_idp_project, partner: other_partner) }

  def delete_project(project_id, which_user = admin)
    delete "/api/v1/ti_idp/projects/#{project_id}", {}, as_user(which_user)
  end

  context 'when user is partner admin' do
    it 'deletes the specified IDP project' do
      project_ids = ::Ti::IdpProject.all.ids
      expect(project_ids.size).to eq 2
      expect(project_ids).to match_array([project.id, other_project.id])

      delete_project(project.id)
      expect_response :ok
      new_ids = ::Ti::IdpProject.all.ids
      expect(new_ids.size).to eq 1
      expect(new_ids).to match_array([other_project.id])
    end

    it 'returns not found if the project does not exist' do
      delete_project(0)
      expect_response :not_found
    end

    it 'returns not found if the project belongs to a different partner' do
      delete_project(other_project.id)
      expect_response :not_found
    end
  end

  context 'when user is not partner admin' do
    it 'returns unauthorized' do
      delete_project(project.id, student)
      expect_response(:forbidden)
    end
  end
end