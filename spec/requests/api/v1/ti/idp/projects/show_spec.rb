# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Ti::IdpProjects#show', type: :request do
  let(:partner) { create(:partner) }
  let(:other_partner) { create(:partner) }
  let(:admin) { create(:user, :admin, partner_id: partner.id) }
  let(:other_admin) { create(:user, :admin, partner_id: other_partner.id) }
  let(:student) { create(:user, job_title: 'Product Engineer') }
  let!(:project) { create(:ti_idp_project, partner: admin.partner) }
  let!(:other_project) { create(:ti_idp_project, partner_id: other_partner.id) }

  def show_project(project_id, which_user = admin)
    get "/api/v1/ti_idp/projects/#{project_id}", {}, as_user(which_user)
  end

  context 'when user is partner admin' do
    before do
      project.update!(
        name: 'IDP X',
        status: :done
      )

      ::Ti::IdpProjectUser.create!(
        project:,
        user: student,
        competency_gaps: [
          {
            competency_id: 1,
            competency_name: "Leadership",
            gap_percentage: 25
          },
          {
            competency_id: 2,
            competency_name: "Communication",
            gap_percentage: 15
          }
        ],
        objectives: ["Improve leadership skills", "Enhance communication abilities"],
        ai_recommendation: {
          task_challenges: [
            {
              task_challenge: "Lead a team project"
            }
          ],
          mentorships: [
            {
              mentorship: "Weekly sessions with a senior leader"
            }
          ],
          training_workshops: [
            {
              training_workshop: "Advanced Communication Skills Workshop"
            }
          ]
        }
      )
    end

    it 'returns the specified IDP project' do
      show_project(project.id)
      expect_response :ok

      expect(response_data['id']).to eq project.id
      expect(response_data['name']).to eq project.name
      expect(response_data['users'].size).to eq 1

      user = response_data['users'].first
      expect(user['id']).to eq student.id
      expect(user['name']).to eq student.name
      expect(user['role_name']).to eq 'Product Engineer'

      expect(user['competency_gaps']).to match_array(
        [
          'Leadership (25%)',
          'Communication (15%)'
        ]
      )

      expect(user['objectives']).to match_array(
        [
          "Improve leadership skills",
          "Enhance communication abilities"
        ]
      )

      expect(user['task_challenges']).to match_array(
        ['Lead a team project']
      )

      expect(user['mentorships']).to match_array(
        ['Weekly sessions with a senior leader']
      )

      expect(user['training_workshops']).to match_array(
        ['Advanced Communication Skills Workshop']
      )
    end

    it 'returns not found if the project does not exist' do
      show_project(0)
      expect_response :not_found
    end

    it 'returns not found if the project belongs to a different partner' do
      show_project(other_project.id)
      expect_response :not_found

      show_project(other_project.id, other_admin)
      expect_response :ok
    end
  end

  context 'when user is not partner admin' do
    it 'returns unauthorized' do
      show_project(project.id, student)
      expect_response(:forbidden)
    end
  end
end