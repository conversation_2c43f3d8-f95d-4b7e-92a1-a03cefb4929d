# frozen_string_literal: true

module V1
  module Ai
    class LgdOutput < ::ApiOutput
      def format
        {
          id: @object.id,
          status: @object.status,
          thread_id: @object.thread_id
        }
      end

      def status_format
        {
          id: @object.id,
          status: @object.status,
          transcription_status: @object.transcription_status,
          analysis_status: @object.analysis_status,
          video_processing_status: @object.video_processing_status,
          thread_id: @object.thread_id
        }
      end

      def transcript_format
        {
          user_id: @object.user_id,
          user_name: @object.user_name,
          user_profile_picture_url: @object.user_profile_picture_url,
          timestamp: @object.timestamp,
          transcript: @object.transcript
        }
      end

      def analysis_format
        {
          user_id: @object.user_id,
          user_name: @object.user_name,
          user_profile_picture_url: @object.user_profile_picture,
          competencies: competencies_output,
          summary: @object.summary
        }
      end

      def metadata_format
        {
          video_url: @object.video_url,
          video_duration: @object.video_duration,
          position: @object.position,
          participants: participants_output
        }
      end

      private

      def competencies_output
        @object.competencies.map do |competency|
          {
            id: competency.id,
            name: competency.name,
            score: competency.score,
            details: competency.details.sort_by { _1[:level] }
          }
        end
      end

      def aspect_details_output(aspect_details)
        aspect_details.map do |aspect_detail|
          {
            name: aspect_detail.name,
            evidence: aspect_detail.evidence,
            timestamp: aspect_detail.timestamp,
            description: aspect_detail.description
          }
        end
      end

      def participants_output
        @object.participants.map do |participant|
          {
            id: participant.id,
            name: participant.name,
            profile_picture_url: participant.profile_picture_url
          }
        end
      end
    end
  end
end
