# frozen_string_literal: true

module V1
  module Ti
    class IdpProjectOutput < ApiOutput
      def format
        {
          id: @object.id,
          name: @object.name
        }
      end

      def list_format
        format.merge(
          created_at: @object.created_at,
          users_count: @object.try(:users_count)
        )
      end

      def show_format
        list_format.merge(
          status: @object.status,
          users: users_output
        )
      end

      private

      def users_output
        # TODO: Fix N+1
        @object.ti_idp_project_users.map do |user|
          task_challenges = []
          mentorships = []
          training_workshops = []

          user.ai_recommendation.each do |key, values|
            values.each do |value|
              case key
              when 'task_challenges'
                task_challenges << value['task_challenge']
              when 'mentorships'
                mentorships << value['mentorship']
              when 'training_workshops'
                training_workshops << value['training_workshop']
              end
            end
          end

          role = PartnerAttribute.find_by(
            key: :role,
            partner_id: @object.partner_id
          )

          upa = UserPartnerAttribute.find_by(
            user_id: user.user_id,
            partner_attribute_id: role&.id
          )

          role_name = upa&.string_value.presence || user.user.job_title

          {
            id: user.user_id,
            name: user.user.name,
            role_name:,
            competency_gaps: user.competency_gaps.map do |cg|
              "#{cg['competency_name']} (#{cg['gap_percentage']}%)"
            end,
            objectives: user.objectives,
            task_challenges:,
            mentorships:,
            training_workshops:
          }
        end
      end
    end
  end
end

