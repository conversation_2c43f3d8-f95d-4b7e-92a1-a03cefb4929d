# frozen_string_literal: true

module Api
  module V1
    module Ti
      class IdpProjectsController < ApiController
        authorize_auth_token! %w[admin]

        def index
          projects = service.all
          render_json_array projects, use: :list_format
        end

        def show
          project = service.show(params[:id])
          render_json project, use: :show_format
        end

        def create
          input = ::V1::Ti::IdpProjectCreationInput.new(request_body)
          validate! input, capture_failure: true

          project = service.create(input.output)
          render_json project, status: :created
        end

        def update
          input = ::V1::Ti::IdpProjectUpdateInput.new(request_body)
          validate! input, capture_failure: true

          project = service.update(params[:id], input.output)
          render_json project
        end

        def destroy
          service.delete(params[:id])
          render_empty_json({})
        end

        private

        def service
          @service ||= ::Ti::IdpProjectService.new(current_user)
        end

        def default_output
          ::V1::Ti::IdpProjectOutput
        end
      end
    end
  end
end
