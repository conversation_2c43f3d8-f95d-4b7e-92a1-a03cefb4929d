# frozen_string_literal: true

module Api
  module V1
    module Ti
      class CompetenciesController < ApplicationController
        authorize_auth_token! %w[admin]

        def distributions
          result = service.distributions(query_params)
          render_json_array result, use: :format
        end

        def overall
          result = service.overall(query_params)
          render_json_array result, use: :format
        end

        def users
          result = service.users(query_params)
          render_json_array result, status: :ok
        end

        def heatmap
          result = service.heatmap(query_params)
          render_json_array result, use: :format
        end

        private

        def service
          @service ||= ::Ti::CompetencyService.new
        end

        def default_output
          ::V1::Ti::CompetencyOutput
        end
      end
    end
  end
end
