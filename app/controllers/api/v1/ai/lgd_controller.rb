# frozen_string_literal: true

module Api
  module V1
    module Ai
      class LgdController < ApplicationController
        authorize_auth_token! [:all]

        def analyze
          input = ::V1::Ai::LgdAnalyzeInput.new(request_body)
          validate! input, capture_failure: true

          result = service.analyze(input.output)
          render_json result, use: :format, status: :created
        end

        def status
          result = service.status(path_params[:id])
          render_json result, use: :status_format
        end

        def transcript
          result = service.transcript(path_params[:id])
          render_json_array result, use: :transcript_format
        end

        def analysis
          result = service.analysis(path_params[:id])
          render_json_array result, use: :analysis_format
        end

        def metadata
          result = service.metadata(path_params[:id])
          render_json_array result, use: :metadata_format
        end

        private

        def service
          @service ||= ::Ai::LgdService.new(current_user)
        end

        def default_output
          ::V1::Ai::LgdOutput
        end
      end
    end
  end
end
