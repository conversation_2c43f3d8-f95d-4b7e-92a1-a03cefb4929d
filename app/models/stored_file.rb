# frozen_string_literal: true

module StoredFile
  module_function

  PRESIGNED_URL_EXPIRATION_TIME = (ENV['AWS_PRESIGN_EXPIRES_IN'].presence || 1.hour).to_i

  def download_url(url)
    new(url).download_url
  end

  def new(uri, service: :s3)
    bucket = FileStorageBucket.new(service:)
    bucket_url = FileStorageBucket.url(service:)
    return self::Unstored.new(uri) if uri.blank?

    uri = uri.strip.gsub(/s3-([\w-]+).amazonaws.com/, 's3.\\1.amazonaws.com')

    is_url = begin
      URI.parse(uri).scheme
    rescue StandardError
      false
    end

    is_stored = uri.start_with?(bucket_url)
    path = uri.gsub(/\A#{bucket_url}/, '').delete_prefix('/')

    self::Unstored.new(uri) if is_url && (!is_stored || uri.match?(/\?/))

    case service
    when :gcs
      # remove the leading slash from the path
      self::GCS.new(bucket.file(CGI.unescape(path), skip_lookup: true))
    else
      self::S3.new(bucket.object(CGI.unescape(path)))
    end
  end

  class S3
    def initialize(file)
      @file = file
    end

    def stored?
      client = @file.client
      res = client.head_object(bucket: @file.bucket_name, key: path)
      res.present?
    rescue Aws::S3::Errors::NotFound
      false
    end

    def basename
      File.basename(path)
    end

    def path
      @file.key
    end

    def size
      @file.content_length
    rescue Aws::S3::Errors::NotFound
      0
    end

    def url
      @file.public_url
    end

    def upload_url
      params = {}
      params[:expires_in] = PRESIGNED_URL_EXPIRATION_TIME
      params[:acl] = 'public-read'
      @file.presigned_url(:put, params)
    end

    def download_url(filename: nil)
      params = {}
      params[:expires_in] ||= PRESIGNED_URL_EXPIRATION_TIME
      params[:response_content_type] = 'application/octet-stream' if filename

      @file.presigned_url(:get, params)
    end

    def content_type
      @file.content_type
    rescue Aws::S3::Errors::NotFound
      nil
    end
  end

  class GCS
    def initialize(file)
      @file = file
    end

    def stored?
      @file.exists?
    rescue StandardError
      false
    end

    def basename
      File.basename(path)
    end

    def path
      @file.name
    end

    def size
      @file.size
    rescue StandardError
      0
    end

    def url
      @file.public_url
    end

    def upload_url
      params = {}
      params[:version] = :v4
      params[:expires] = PRESIGNED_URL_EXPIRATION_TIME

      @file.signed_url(method: 'PUT', **params)
    end

    def download_url(filename: nil, expires: nil)
      params = {}
      params[:expires] = expires || PRESIGNED_URL_EXPIRATION_TIME
      params[:version] = :v4
      params[:response_content_type] = 'application/octet-stream' if filename

      @file.signed_url(method: 'GET', **params)
    end

    def content_type
      @file.content_type
    rescue StandardError
      nil
    end
  end

  class Unstored
    def initialize(url)
      @url = url
    end

    def url(*)
      @url
    end

    alias upload_url url
    alias download_url url

    def basename
      File.basename(@url)
    end

    def path
      URI.parse(@url).path
    end

    def size
      0
    end

    def content_type
      nil
    end
  end
end
