# frozen_string_literal: true

module Ti
  class IdpProjectService < AppService
    def initialize(user)
      @user = user
      @projects = ::Ti::IdpProjects.new
    end

    def all
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      includes = %w[users_count]
      filter = { partner_id: @user.partner_id }
      @projects.include(*includes).filter(**filter)
    end

    def create(params)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      project = nil
      user_ids = params[:user_ids]
      validate_user_ids(user_ids)

      ActiveRecord::Base.transaction do
        project = ::Ti::IdpProject.create!(
          partner_id: @user.partner_id,
          name: params[:name],
          status: :queued
        )

        users_records = user_ids.map do |user_id|
          {
            ti_idp_project_id: project.id,
            user_id:
          }
        end

        ::Ti::IdpProjectUser.import users_records
        ::Ti::IdpProjectRecommendationWorker.perform_async(project.id)
      end

      project
    end

    def update(id, params)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      project = ::Ti::IdpProject.find_by(id:, partner_id: @user.partner_id)
      exist!(project, on_error: 'Idp project not found')

      project.update!(params)
      project
    end

    def show(id)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      filter = { id:, partner_id: @user.partner_id }
      projects = @projects.filter(**filter)

      project = projects.first
      exist!(project, on_error: 'Idp project not found')

      project
    end

    def delete(id)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      project = ::Ti::IdpProject.find_by!(id:, partner_id: @user.partner_id)
      project.discard!
    end

    private

    def validate_user_ids(user_ids)
      assert!(
        user_ids.size.positive?,
        on_error: "User ID(s) can't be blank."
      )

      assert!(
        user_ids.size == User.where(
          id: user_ids,
          partner_id: @user.partner_id
        ).count,
        on_error: <<~MSG.squish
          Invalid User ID(s).
          Please ensure all provided User IDs exist and belong to the same partner_id.
        MSG
      )
    end
  end
end
