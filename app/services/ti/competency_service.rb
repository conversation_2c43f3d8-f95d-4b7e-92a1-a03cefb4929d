# frozen_string_literal: true

module Ti
  class CompetencyService < AppService
    def initialize
      @client = ::Ti::BigqueryClientService.new.data_mart
    end

    def distributions(params = {})
      group_by = params.delete(:group_by)
      gap = params.delete(:gap)
      allowed_group_by = %w[competency_category division job_level_id]

      assert! allowed_group_by.include?(group_by), on_error: 'Invalid group_by parameter'

      sql_query = if group_by == 'competency_category'
                    <<~SQL
                      SELECT
                        DISTINCT(competency_category) AS label,
                        AVG(#{gap ? '100 - competency_match' : 'competency_match'}) AS avg_total_comp_score
                      FROM `mart_employee_competency_gap_internal`
                      GROUP BY competency_category
                    SQL
                  elsif group_by == 'job_level_id'
                    <<~SQL
                      WITH pivoted AS (
                        SELECT
                          `mart_employee_competency_gap_internal`.name AS user_name,
                          `rakamin-poc.data_warehouse.dim_job_level_internal`.name AS job_level_name,
                          `mart_employee_competency_gap_internal`.avg_competency_match_per_employee AS avg_competency_match_per_employee
                        FROM
                          `mart_employee_competency_gap_internal`
                          INNER JOIN `mart_employee_competency_internal` ON
                            `mart_employee_competency_gap_internal`.user_id = `mart_employee_competency_internal`.user_id
                          INNER JOIN `rakamin-poc.data_warehouse.dim_job_level_internal` ON
                            `mart_employee_competency_internal`.job_level_id = `rakamin-poc.data_warehouse.dim_job_level_internal`.id
                        GROUP BY
                          `mart_employee_competency_gap_internal`.name,
                          `rakamin-poc.data_warehouse.dim_job_level_internal`.name,
                          `mart_employee_competency_gap_internal`.avg_competency_match_per_employee
                      )
                      SELECT DISTINCT(job_level_name) AS label, AVG(#{gap ? '100 - ' : ''}avg_competency_match_per_employee) AS avg_total_comp_score
                      FROM pivoted
                      GROUP BY job_level_name
                    SQL
                  elsif group_by == 'division'
                    <<~SQL
                      WITH pivoted AS (
                        SELECT
                          `mart_employee_competency_gap_internal`.name AS user_name,
                          `mart_employee_competency_gap_internal`.division AS division,
                          `mart_employee_competency_gap_internal`.avg_competency_match_per_employee AS avg_competency_match_per_employee
                        FROM
                          `mart_employee_competency_gap_internal`
                        GROUP BY
                          `mart_employee_competency_gap_internal`.name,
                          `mart_employee_competency_gap_internal`.division,
                          `mart_employee_competency_gap_internal`.avg_competency_match_per_employee
                      )
                      SELECT DISTINCT(division) AS label, AVG(#{gap ? '100 - ' : ''}avg_competency_match_per_employee) AS avg_total_comp_score
                      FROM pivoted
                      GROUP BY division
                    SQL
                  end

      @client.query(
        sql_query
      )
    end

    def users(params = {})
      gap = params.delete(:gap)
      sql_column_competency_categories = competency_categories_data(gap: gap).map do |data|
        data[:sql_query]
      end.join(',')

      sql_query = <<~SQL.squish
        SELECT
          gap.name,
          gap.employee_id,
          gap.user_id,
          gap.role_name,
          gap.division,
          #{sql_column_competency_categories}
        FROM
          `mart_employee_competency_gap_internal` AS gap
          INNER JOIN `mart_employee_competency_internal` AS employees ON employees.user_id = gap.user_id
          INNER JOIN `data_warehouse.dim_job_level_internal` AS job_levels ON job_levels.id = employees.job_level_id
        GROUP BY
          gap.name,
          gap.employee_id,
          gap.user_id,
          gap.role_name,
          gap.division
        LIMIT 100
      SQL

      @client.query(
        sql_query,
        params: params
      )
    end

    def overall(params = {})
      allowed_params = %w[
        overall_competencies_achievement
        leaderboard_competencies
        gap
      ]

      param_keys = params.keys
      assert! (allowed_params & param_keys).any?, on_error: 'Invalid parameters'

      if params[:overall_competencies_achievement]
        return overall_competencies_achievement(gap: params[:gap])
      end

      return leaderboard_competencies(gap: params[:gap]) if params[:leaderboard_competencies]

      nil
    end

    def heatmap(params = {})
      gap = params.delete(:gap)

      sql_query = <<~SQL.squish
        WITH divisions AS
          ( SELECT distinct(division) AS division
          FROM `mart_employee_competency_gap_internal`),
            job_levels AS
          ( SELECT name AS job_level_name
          FROM `rakamin-poc.data_warehouse.dim_job_level_internal`),
            merged_div_job_levels AS
          ( SELECT *
          FROM divisions
          CROSS JOIN job_levels),
            pivoted AS
          ( SELECT `mart_employee_competency_gap_internal`.division AS division,
                  jli.name AS job_level_name,
                  `mart_employee_competency_gap_internal`.avg_competency_match_per_employee AS avg_competency_match_per_employee
          FROM `mart_employee_competency_gap_internal`
          INNER JOIN `mart_employee_competency_internal` meci ON meci.user_id = `mart_employee_competency_gap_internal`.user_id
          INNER JOIN `data_warehouse.dim_job_level_internal` jli ON jli.id = meci.job_level_id
          GROUP BY `mart_employee_competency_gap_internal`.division,
                    jli.name,
                    `mart_employee_competency_gap_internal`.avg_competency_match_per_employee),
          averaged_pivoted AS
          (
            SELECT DISTINCT job_level_name,
                            division,
                            #{gap ? '100 - ' : ''}avg(avg_competency_match_per_employee) AS average
            FROM pivoted
            GROUP BY job_level_name,
                      division
          )
        SELECT md.division,
              md.job_level_name,
              averaged_pivoted.average AS average
        FROM merged_div_job_levels md
        LEFT JOIN averaged_pivoted ON averaged_pivoted.division = md.division
        AND averaged_pivoted.job_level_name = md.job_level_name
      SQL

      @client.query(
        sql_query
      )
    end

    private

    def overall_competencies_achievement(gap: false)
      sql_query = <<~SQL.squish
        SELECT
          ROUND(AVG(#{gap ? '100 - ' : ''}avg_competency_match_per_employee), 2) AS overall_competency_achievement
        FROM
          (
            SELECT DISTINCT name, avg_competency_match_per_employee
            FROM `mart_employee_competency_gap_internal`
          )
      SQL

      @client.query(
        sql_query
      )
    end

    def leaderboard_competencies(gap: false)
      sql_column_competency_categories = competency_categories_data(gap: gap).map do |data|
        data[:sql_query]
      end.join(',')

      sql_avg_competency_categories = competency_categories_data(gap: gap).map do |data|
        snake_cased_category = data[:snake_cased_category]

        <<~SQL.squish
          AVG(#{snake_cased_category}) AS avg_#{snake_cased_category}
        SQL
      end.join(',')

      sql_query = <<~SQL.squish
        WITH pivoted AS (
          SELECT
            user_id,
            employee_id,
            name,
            division,
            #{sql_column_competency_categories}
          FROM
            `mart_employee_competency_gap_internal`
          GROUP BY
            user_id, employee_id, name, division
        )

        SELECT
          #{sql_avg_competency_categories}
        FROM
          pivoted
      SQL

      categories = competency_categories_data(gap: gap).map do |data|
        data[:category]
      end

      result = @client.query(
        sql_query
      )

      categories.map do |category|
        result_data = result.first.to_a.find do |row|
          row.first.to_s.include?(category.downcase.tr(' ', '_').gsub('&', 'and'))
        end

        {
          label: category,
          value: result_data.last
        }
      end
    end

    def competency_categories_data(gap: false)
      sql_competency_categories = <<~SQL.squish
        SELECT
          DISTINCT(competency_category)
        FROM
          mart_employee_competency_gap_internal
      SQL

      competency_categories = @client.query(
        sql_competency_categories
      ).map do |row|
        row[:competency_category]
      end

      competency_categories.map do |category|
        # Convert to snake_case, replace spaces with underscores, and '&' with 'and' for SQL safety
        snake_cased_category = category.downcase.tr(' ', '_').gsub('&', 'and')

        field_value = 'competency_match'
        field_value = '100 - competency_match' if gap

        sql_query = <<~SQL.squish
          MAX(CASE WHEN competency_category = '#{category}' THEN #{field_value} END) AS #{snake_cased_category}
        SQL

        {
          sql_query: sql_query,
          snake_cased_category: snake_cased_category,
          category: category
        }
      end
    end
  end
end
