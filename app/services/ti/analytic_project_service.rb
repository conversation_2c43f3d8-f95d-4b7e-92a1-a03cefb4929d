# frozen_string_literal: true

module Ti
  class AnalyticProjectService < AppService
    def initialize(user)
      @user = user
      @partner = Partner.find_by(id: @user.partner_id)
      @projects = ::Ti::AnalyticProjects.new
    end

    def all
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      includes = %w[comparison]
      filter = { partner_id: @user.partner_id }
      projects = @projects.include(*includes).filter(**filter)
      partner_attributes = PartnerAttribute.where(**filter)

      AppStruct.new(
        projects:,
        partner_attributes:
      )
    end

    def create(params)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      @partner_attributes = []
      validate_variables!(params[:x_variable_id], params[:y_variable_id])
      validate_groups!(params[:groups])
      validate_filters!(params[:filters])
      validate_comparison_name!(
        params[:comparison_name],
        params[:x_variable_id],
        params[:y_variable_id]
      )

      project_params = {
        partner_id: @user.partner_id,
        title: params[:title],
        context: params[:context],
        x_variable_id: params[:x_variable_id],
        y_variable_id: params[:y_variable_id],
        groups: params[:groups],
        filters: params[:filters],
        comparison_name: params[:comparison_name],
        norm_mappings: params[:norm_mappings]
      }

      project = ::Ti::AnalyticProject.create!(project_params)
      partner_attributes = @partner_attributes
      ::Ti::CalculateAnalyticProjectMetadataWorker.perform_async(project.id)

      AppStruct.new(
        project:,
        partner_attributes:
      )
    end

    def update(id, params)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      @partner_attributes = []
      project = ::Ti::AnalyticProject.find_by(id: id, partner_id: @user.partner_id)
      exist!(project, on_error: 'Analytic project not found')

      if params[:x_variable_id].present? || params[:y_variable_id].present?
        x_variable_id = params[:x_variable_id] || project.x_variable_id
        y_variable_id = params[:y_variable_id] || project.y_variable_id
        validate_variables!(x_variable_id, y_variable_id)
      end

      validate_groups!(params[:groups])
      validate_filters!(params[:filters])

      if params[:comparison_name].present? || params[:x_variable_id].present? || params[:y_variable_id].present?
        comparison_name = params[:comparison_name] || project.comparison_name
        x_variable_id = params[:x_variable_id] || project.x_variable_id
        y_variable_id = params[:y_variable_id] || project.y_variable_id
        validate_comparison_name!(comparison_name, x_variable_id, y_variable_id)
      end

      project.update!(params.compact)
      partner_attributes = @partner_attributes
      ::Ti::CalculateAnalyticProjectMetadataWorker.perform_async(project.id)

      AppStruct.new(
        project:,
        partner_attributes:
      )
    end

    def show(id)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      includes = %w[comparison]
      filter = { id:, partner_id: @user.partner_id }
      projects = @projects.include(*includes).filter(**filter)
      partner_attributes = PartnerAttribute.where(**filter.except(:id))

      project = projects.first
      exist!(project, on_error: 'Analytic project not found')

      AppStruct.new(
        project:,
        partner_attributes:
      )
    end

    def delete(id)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      project = ::Ti::AnalyticProject.find_by!(id:, partner_id: @user.partner_id)
      project.discard!
    end

    def fetch_attributes(type)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      return [] unless @partner&.ti_analytic_attributes&.key?(type)

      attribute_keys = @partner.ti_analytic_attributes[type] || []
      PartnerAttribute.where(partner_id: @user.partner_id, key: attribute_keys)
    end

    def talent_distribution(params)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      repo = TalentDistributions.new
      includes = %i[employee_id role]

      if params[:project_id].present?
        id = params[:project_id].to_i
        partner_id = @user.partner_id
        project = ::Ti::AnalyticProject.find_by(id:, partner_id:)
        return [] unless project

        filters = {
          x_variable_id: project.x_variable_id,
          y_variable_id: project.y_variable_id
        }

        project.groups&.each do |key, values|
          filters[key.to_sym] = values if values.present?
        end

        project.filters&.each do |key, values|
          filters[key.to_sym] = values if values.present?
        end
      end

      filters ||= params
      filters[:partner_id] = @user.partner_id
      filters[:not_role] = :admin

      if filters[:disable_pagination].to_s.blank?
        filters[:disable_pagination] = true
      end

      repo.include(*includes).filter(**filters)
    end

    def comparisons(params)
      authorize! as_partner_admin?(@user), on_error: 'Not allowed to access this resource'

      x_variable_id = params[:x_variable_id].to_i
      y_variable_id = params[:y_variable_id].to_i

      return [] if PartnerAttribute.where(
        id: [x_variable_id, y_variable_id],
        partner_id: @user.partner_id
      ).count != 2

      columns_sql = <<~SQL.squish
        name,
        MAX(CASE WHEN variable_id = (:x_variable_id)::int THEN value ELSE NULL END) AS x_value,
        MAX(CASE WHEN variable_id = (:y_variable_id)::int THEN value ELSE NULL END) AS y_value
      SQL

      columns = ActiveRecord::Base.sanitize_sql [
        columns_sql,
        { x_variable_id:, y_variable_id: }
      ]

      where = {
        name: params[:comparison_name].to_s.strip.presence,
        variable_id: [x_variable_id, y_variable_id]
      }.compact

      ::Ti::AnalyticComparison.where(**where).select(columns).group(:name)
    end

    private

    def validate_variables!(x_variable_id, y_variable_id)
      ti_analytic_attributes = @partner.ti_analytic_attributes
      valid_variable_keys = ti_analytic_attributes['variables'] || []

      attributes = PartnerAttribute.where(
        id: [x_variable_id, y_variable_id],
        partner_id: @user.partner_id
      )

      x_variable = attributes.find { _1.id == x_variable_id }
      exist!(valid_variable_keys.include?(x_variable&.key), on_error: 'X variable not found')

      y_variable = attributes.find { _1.id == y_variable_id }
      exist!(valid_variable_keys.include?(y_variable&.key), on_error: 'Y variable not found')

      @partner_attributes += attributes
    end

    def validate_groups!(groups)
      validate_attributes!(
        attributes: groups,
        valid_keys: @partner.ti_analytic_attributes['groups'] || [],
        not_found_error: 'Group not found',
        invalid_value_error: 'Group value is invalid'
      )
    end

    def validate_filters!(filters)
      validate_attributes!(
        attributes: filters,
        valid_keys: @partner.ti_analytic_attributes['filters'] || [],
        not_found_error: 'Filter not found',
        invalid_value_error: 'Filter value is invalid'
      )
    end

    def validate_attributes!(attributes:, valid_keys:, not_found_error:, invalid_value_error:)
      return if attributes.blank?

      partner_attributes = PartnerAttribute.where(
        key: attributes.keys.map(&:to_s),
        partner_id: @user.partner_id
      )

      attributes.each do |key, values|
        attribute = partner_attributes.find { _1.key == key }
        exist!(valid_keys.include?(attribute&.key), on_error: not_found_error)

        valid_values = attribute.enum_mappings.keys.map(&:to_s)
        all_values_exist = values.all? { |value| value.in?(valid_values) }
        assert!(all_values_exist, on_error: invalid_value_error)
      end

      @partner_attributes += partner_attributes
    end

    def validate_comparison_name!(comparison_name, x_variable_id, y_variable_id)
      return if comparison_name.blank?

      comparison_attrs = ::Ti::AnalyticComparison.where(
        variable_id: [x_variable_id, y_variable_id],
        name: comparison_name
      )

      on_error = 'Comparison not found for this variable combination'
      exist!(comparison_attrs.count == 2, on_error:)
    end
  end
end
