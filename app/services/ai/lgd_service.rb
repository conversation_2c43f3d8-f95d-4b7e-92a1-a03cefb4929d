# frozen_string_literal: true

module Ai
  class LgdService < ::AppService
    def initialize(user)
      @user = user
    end

    def analyze(params)
      parent_assistant_name = 'lgd_assistant'
      parent_assistant = Ai::Assistant.find_by!(purpose: parent_assistant_name)

      mapped_main_workers = fetch_main_workers!

      user_ids = params[:user_ids]
      users = User.where(id: user_ids)

      exist! users.size == user_ids.size, on_error: 'Terdapat user yang tidak ditemukan'
      thread_id = SecureRandom.uuid

      saved_params = params.except(:user_ids)

      saved_params[:users] = users.map do |user|
        {
          id: user.id,
          name: user.name,
          email: user.email
        }
      end

      parent_run = Ai::AssistantRun.create!(
        thread_id: thread_id,
        ai_assistant_id: parent_assistant.id,
        file_url: params[:video_url],
        request_raw: saved_params,
        status: :queued,
        user_id: @user.id
      )

      records = mapped_main_workers.map do |_, assistant_id|
        Ai::AssistantRun.new(
          thread_id: thread_id,
          ai_assistant_id: assistant_id,
          user_id: @user.id,
          status: :queued
        )
      end

      Ai::AssistantRun.import(records)

      Lgd::MainWorker.perform_async(parent_run.id)

      parent_run
    end

    def status(id)
      run = Ai::AssistantRun.find(id)
      thread_id = run.thread_id
      current_status_by_purpose = {}

      main_worker_purposes = main_workers_map.values
      Ai::AssistantRun.joins(:assistant)
                      .where(thread_id: thread_id, ai_assistants: { purpose: main_worker_purposes })
                      .select('ai_assistant_runs.status AS run_status, ai_assistants.purpose AS assistant_purpose')
                      .each do |record|
                        current_status_by_purpose[record.assistant_purpose] = record.run_status
                      end

      result_hash = AppStruct.new
      result_hash[:thread_id] = thread_id
      result_hash[:id] = run.id
      result_hash[:status] = run.status

      main_workers_map.each do |status_key, worker_purpose|
        result_hash[status_key] = current_status_by_purpose[worker_purpose] || 'queued'
      end

      result_hash
    end

    def transcript(id)
      main_run = Ai::AssistantRun.find(id)
      thread_id = main_run.thread_id

      assistant_transcriber = Ai::Assistant.find_by!(purpose: 'lgd_transcriber')
      transcription_run = Ai::AssistantRun.find_by!(thread_id:,
                                                    ai_assistant_id: assistant_transcriber.id)

      compiled_transcript = transcription_run.json_response['compiled_transcript']
      exist! compiled_transcript.present?, on_error: 'Transkrip tidak ditemukan'

      parsed_transcript = if compiled_transcript.is_a?(String)
                            safe_parse_json
                          else
                            compiled_transcript
                          end

      user_ids = parsed_transcript.map { _1['user_id'] }.compact.uniq
      users = User.where(id: user_ids)

      exist! users.size == user_ids.size, on_error: 'Terdapat user yang tidak ditemukan'

      results = []
      parsed_transcript.each do |transcript|
        user = users.find { |u| u.id == transcript['user_id'].to_i }

        next unless user

        results << AppStruct.new(
          user_id: user.id,
          user_name: user.name,
          user_profile_picture: user.profile_picture_url,
          timestamp: transcript['timestamp'],
          transcript: transcript['transcript']
        )
      end

      results
    end

    def analysis(id)
      main_run = Ai::AssistantRun.find(id)
      thread_id = main_run.thread_id

      assistant_analyzer = Ai::Assistant.find_by!(purpose: 'lgd_analyzer')
      analysis_run = Ai::AssistantRun.find_by!(thread_id:,
                                               ai_assistant_id: assistant_analyzer.id)

      formatted_results = analysis_run.json_response['formatted_analysis_result']
      exist! formatted_results.present?, on_error: 'Analisis tidak ditemukan'

      summary_results = analysis_run.json_response['summarized_analysis_result']
      exist! summary_results.present?, on_error: 'Analisis tidak ditemukan'

      parsed_formatted_result = if formatted_results.is_a?(String)
                                  safe_parse_json(formatted_results)
                                else
                                  formatted_results
                                end

      parsed_summary_result = if summary_results.is_a?(String)
                                safe_parse_json(summary_results)
                              else
                                summary_results
                              end

      user_ids = parsed_formatted_result.map { _1['user_id'] }.compact.uniq
      user_ids += parsed_summary_result.map { _1['user_id'] }.compact.uniq
      user_ids.uniq!

      users = User.where(id: user_ids)
      exist! users.size == user_ids.size, on_error: 'Terdapat user yang tidak ditemukan'

      detail_scores = parsed_formatted_result.flat_map { _1['detail_scores'] }
      unique_competencies = detail_scores.flatten.map { _1['competency_name'] }.uniq
      exist! unique_competencies.present?, on_error: 'Tidak ada kompetensi yang ditemukan'

      mapped_competencies = {}
      unique_competencies.each do |competency_name|
        mapped_competencies[competency_name] = SecureRandom.uuid
      end

      results = []
      lgd_competencies = LocaleCache.new.properties['lgd_competencies']

      users.each do |user|
        formatted_result = parsed_formatted_result.find { |r| r['user_id'] == user.id }
        summary_result = parsed_summary_result.find { |r| r['user_id'] == user.id }
        next unless formatted_result && summary_result

        current_competencies = lgd_competencies.map do |lgd_competency|
          current_detail = formatted_result['detail_scores'].find do |detail_score|
            detail_score['competency_name'] == lgd_competency['competency']
          end

          aspect_details = (1..5).map do |level|
            current_comp_level = lgd_competency['levels'].find do |level_obj|
              level_obj['order_level'].to_i == level
            end

            key_behaviours = current_comp_level['key_behaviors'].map do |kb_obj|
              achieved_kb = current_detail['aspect_details'].find do |aspect_detail|
                aspect_detail['name'] == kb_obj['name'] &&
                  aspect_detail['level'].to_i == level
              end

              kb_evidences = achieved_kb['evidences'].presence if achieved_kb
              kb_evidences ||= []

              {
                name: kb_obj['name'],
                evidences: kb_evidences
              }
            end

            { level:, key_behaviours: }
          end

          AppStruct.new(
            id: mapped_competencies[current_detail['competency_name']],
            name: current_detail['competency_name'],
            score: current_detail['average_score'],
            details: aspect_details
          )
        end

        results << AppStruct.new(
          user_id: user.id,
          user_name: user.name,
          user_profile_picture: user.profile_picture_url,
          competencies: current_competencies,
          summary: summary_result['summary']
        )
      end

      results
    end

    def metadata(id)
      main_run = Ai::AssistantRun.find(id)
      thread_id = main_run.thread_id

      file = StoredFile.new(main_run.file_url, service: :gcs)
      public_url = file.download_url(expires: 1.day.to_i)

      video_processor = Ai::Assistant.find_by!(purpose: 'lgd_video_processor')
      video_run = Ai::AssistantRun.find_by!(thread_id:,
                                            ai_assistant_id: video_processor.id)
      video_duration = video_run.json_response['video_duration'].to_i

      participants = main_run.json_response['participants'].map do |participant|
        AppStruct.new(
          id: participant['id'].to_i,
          name: participant['name'],
          profile_picture_url: participant['profile_picture_url']
        )
      end

      AppStruct.new(
        video_url: public_url,
        video_duration: video_duration,
        position: main_run.json_response['position'],
        participants: participants
      )
    end

    private

    def fetch_main_workers!
      main_workers = main_workers_map.values
      workers = Ai::Assistant.where(purpose: main_workers).select(:id, :purpose, :config)

      exist! workers.size == main_workers.size,
             on_error: 'Terdapat worker assistant yang tidak ditemukan'

      mapped_main_workers = {}
      workers.each do |worker|
        mapped_main_workers[worker[:purpose]] = worker[:id]
      end

      mapped_main_workers
    end

    def main_workers_map
      {
        'video_processing_status' => 'lgd_video_processor',
        'transcription_status' => 'lgd_transcriber',
        'analysis_status' => 'lgd_analyzer'
      }
    end
  end
end
