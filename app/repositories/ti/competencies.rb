# frozen_string_literal: true

module Ti
  class Competencies < ApplicationRepository
    private

    def default_scope
      ::Ti::Competency.all.select('ti_competencies.*')
    end

    def filter_by_group_by_name(name)
      @scope.where(group_by_competency_name: name)
    end

    def filter_by_group_by_division(name)
      @scope.where(group_by_division_name: name)
    end

    def filter_by_group_by_job_level(name)
      @scope.where(group_by_job_level_name: name)
    end
  end
end
