# frozen_string_literal: true

module Ti
  class IdpProjects < ApplicationRepository
    private

    def default_scope
      ::Ti::IdpProject.all.select('ti_idp_projects.*')
    end

    def include_users_count
      @scope
        .select(
          <<~SQL.squish
            COUNT(*) FILTER(
              WHERE ti_idp_project_users.user_id IS NOT NULL
            ) AS users_count
          SQL
        )
        .left_joins(:ti_idp_project_users)
        .group('ti_idp_projects.id')
    end

    def filter_by_id(id)
      @scope.where(id:)
    end

    def filter_by_partner_id(partner_id)
      @scope.where(partner_id:)
    end
  end
end
