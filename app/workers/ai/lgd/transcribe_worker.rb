# frozen_string_literal: true

module Ai
  module Lgd
    class TranscribeWorker < ApplicationWorker
      def perform(transcribe_id, main_worker_id)
        transcribe_run = Ai::AssistantRun.find(transcribe_id)
        return unless transcribe_run.status.in?(%w[queued in_progress])

        @main_worker = Ai::AssistantRun.find(main_worker_id)
        @main_worker.transcribing!

        video_processor_run = Ai::AssistantRun.joins(:assistant)
                                              .find_by(
                                                thread_id: transcribe_run.thread_id,
                                                ai_assistants: { purpose: 'lgd_video_processor' }
                                              )

        return unless video_processor_run.present?
        return unless video_processor_run.status == 'completed'

        transcribe_run.in_progress!

        chunk_transcriber_assistant = Ai::Assistant.find_by(purpose: 'lgd_chunk_transcriber')

        chunks_records = []
        # chunked_urls is an array of hashes
        video_processor_run.json_response['chunked_urls'].each do |chunk|
          # Handle both hash and string cases for chunk
          file_url = chunk.is_a?(Hash) ? chunk['url'] : chunk
          next unless file_url # Skip if file_url is nil

          chunk_run = Ai::AssistantRun.find_by(
            thread_id: transcribe_run.thread_id,
            ai_assistant_id: chunk_transcriber_assistant.id,
            file_url: file_url
          )

          chunk_run ||= Ai::AssistantRun.create!(
            thread_id: transcribe_run.thread_id,
            ai_assistant_id: chunk_transcriber_assistant.id,
            status: 'queued',
            user_id: transcribe_run.user_id,
            file_url: chunk['url'],
            request_raw: chunk
          )

          chunks_records << {
            assistant_run_chunk_id: chunk_run.id,
            status: chunk_run.status,
            starts_at: chunk_run.completed? ? chunk_run.created_at : nil,
            completed_at: chunk_run.completed? ? chunk_run.updated_at : nil
          }
        end

        return if chunks_records.empty?

        transcribe_run.update!(request_raw: { chunks: chunks_records })

        chunk_ids = chunks_records.map { |r| r[:assistant_run_chunk_id] }

        chunk_runs = Ai::AssistantRun.where(id: chunk_ids)
                                     .select(
                                       :status, :id, :created_at,
                                       :updated_at
                                     )

        transcribe_completed = chunk_runs.all? { |r| r.status == 'completed' }

        if transcribe_completed
          joined_trans = join_transcripts(chunk_ids)
          compile_transcript(transcribe_run, joined_trans)
        end

        until transcribe_completed
          chunk_runs.each(&:reload)

          chunk_runs.each do |chunk_run|
            chunk_record = chunks_records.find { |r| r[:assistant_run_chunk_id] == chunk_run.id }
            chunk_record[:status] = chunk_run.status
            chunk_record[:starts_at] = chunk_run.created_at if chunk_run.status == 'in_progress'
            chunk_record[:completed_at] = chunk_run.updated_at if chunk_run.status == 'completed'
          end

          in_progress_count = chunks_records.count { |r| r[:status] == 'in_progress' }
          queued_count = chunks_records.count { |r| r[:status] == 'queued' }
          failed_count = chunks_records.count { |r| r[:status] == 'failed' }

          raise 'Chunk Transcription failed' if failed_count.positive?

          if in_progress_count < 10 && queued_count.positive?
            size_to_run = 10 - in_progress_count

            chunks_records.select { |r| r[:status] == 'queued' }
                          .first(size_to_run)
                          .each do |chunk_record|
              chunk_record[:status] = 'in_progress'
              chunk_record[:starts_at] = Time.current

              chunk_id = chunk_record[:assistant_run_chunk_id]
              Ai::Lgd::TranscribeChunkWorker.perform_async(chunk_id, transcribe_id, main_worker_id)
              current_run = Ai::AssistantRun.find(chunk_id)
              current_run.in_progress!
            end
          end

          transcribe_run.update!(request_raw: { chunks: chunks_records })

          transcribe_completed = chunks_records.all? { |r| r[:status] == 'completed' }

          break if transcribe_completed

          sleep 5
        end

        transcribe_run.update!(request_raw: { chunks: chunks_records })

        transcripts = extract_transcripts(chunk_ids)
        compile_transcript(transcribe_run, transcripts)

        transcribe_run.completed!
      rescue StandardError => e
        Sentry.capture_exception(e)

        transcribe_run.update!(
          status: 'failed',
          raw_response: e.message
        )
      end

      private

      def extract_transcripts(chunk_ids)
        chunks = Ai::AssistantRun.where(id: chunk_ids).select(:json_response, :id, :request_raw)

        sorted_chunks = chunks.sort_by { |chunk| chunk.request_raw['start_time'] }
        json_transcripts = []

        sorted_chunks.each do |chunk|
          transcript = chunk.json_response['transcript']
          next unless transcript.present?

          transcript = transcript.sub('```json', '')
          transcript = transcript.sub('```', '')
          parsed_transcript = safe_parse_json(transcript.strip)

          starts_at_second = chunk.request_raw['start_time']
          ends_at_second = chunk.request_raw['end_time']

          parsed_transcript['chunk_meta'] = {
            original_video_chunk_start_time: second_to_time_string(starts_at_second),
            original_video_chunk_end_time: second_to_time_string(ends_at_second)
          }

          json_transcripts << parsed_transcript
        end

        json_transcripts.filter(&:present?)
      end

      def compile_transcript(run, transcripts)
        assistant = run.assistant
        json_response = { transcripts_per_user: [], compiled_transcript: [] }
        raw_responses = []
        total_tokens = 0

        @main_worker.request_raw['users'].each do |user|
          result = compile_transcript_single_user(
            user,
            assistant,
            transcripts
          )

          raw_response = result[:raw_response]
          parsed_response = result[:parsed_response]
          candidate_token = result[:candidate_token]

          json_response[:transcripts_per_user] << {
            user_name: user['name'],
            transcripts: parsed_response
          }

          raw_responses << {
            user_name: user['name'],
            raw_response: raw_response
          }

          json_response[:compiled_transcript] += parsed_response
          total_tokens += candidate_token
        end

        sorted_transcript = json_response[:compiled_transcript].sort_by do |ct|
          starts_at, ends_at = ct['timestamp'].split('-')
          [starts_at.to_time, ends_at.to_time]
        end

        json_response[:compiled_transcript] = sorted_transcript
        run.status = 'completed'
        run.json_response = json_response
        run.raw_response = JSON.dump(raw_responses)
        run.total_token = total_tokens

        current_chunks = run.request_raw['chunks']
        run.request_raw = {
          chunks: current_chunks,
          transcripts:
        }

        run.save!
      end

      def compile_transcript_single_user(user, assistant, transcripts)
        user_id = user['id']
        user_name = user['name']

        string_transcripts = <<~TRANSCRIPT
          Here is the chunked transcript
        TRANSCRIPT

        transcripts.each do |t|
          speaker_profiles_in_chunk = t['speaker_profiles_in_chunk']
          speaker = speaker_profiles_in_chunk.find { _1['label'] == user_name }
          next unless speaker.present?

          current_segments = t['segments'].filter { _1['speaker_label'] == user_name }
          next if current_segments.empty?

          current_chunk = {
            chunk_meta: t['chunk_meta'],
            speaker_profiles_in_chunk: [speaker],
            segments: current_segments
          }

          added_string = <<~STRING
            Transcript:
            ```json
            #{JSON.dump(current_chunk)}
            ```
            \n\n
          STRING

          string_transcripts += added_string
        end

        list_participant_prompts = <<~PROMPT
          Here is the participant lists
          ID: #{user_id}
          Name: #{user_name}
          \n\n
        PROMPT

        user_prompt = <<~PROMPT
          Please compile the following transcript into a single transcript

          #{string_transcripts}

          #{list_participant_prompts}
        PROMPT

        ai_params = {
          model: assistant.model,
          system_instruction: { parts: [{ text: assistant.system_instruction }] },
          contents: [
            {
              role: 'user',
              parts: [{ text: user_prompt }]
            }
          ],
          generation_config: {
            temperature: assistant.temperature,
            response_mime_type: assistant.response_mime_type
          }
        }

        raw_response = vertex_ai_service.generate_content(ai_params)
        text_response = raw_response.dig('candidates', 0, 'content', 'parts', 0, 'text')
        candidate_token = raw_response.dig('usageMetadata', 'totalTokenCount') || 0
        parsed_response = safe_parse_json(text_response)

        {
          raw_response:,
          parsed_response:,
          candidate_token:
        }
      end

      def second_to_time_string(full_seconds)
        hour = (full_seconds / 3600).to_i
        hour = "0#{hour}" if hour < 10

        minute = (full_seconds % 3600).to_i
        minute = "0#{minute}" if minute < 10

        second = (full_seconds % 60).to_i
        second = "0#{second}" if second < 10

        "#{hour}:#{minute}:#{second}"
      end

      def vertex_ai_service
        @vertex_ai_service ||= ::External::VertexAiService.new
      end
    end
  end
end
