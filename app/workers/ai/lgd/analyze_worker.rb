# frozen_string_literal: true

module Ai
  module Lgd
    class AnalyzeWorker < ApplicationWorker
      def perform(analyze_id, main_worker_id)
        setup_runs(analyze_id, main_worker_id)

        raise 'Invalid run status' unless valid_runs?(@run)

        transcription_result = fetch_transcription_result

        if transcription_result.blank?
          @run.update!(status: 'completed', json_response: {})
          @main_worker.update!(status: 'completed')
          return
        end

        @main_worker.analyzing!
        @run.in_progress!

        process_analysis(transcription_result)
      rescue StandardError => e
        handle_error(e)
      end

      private

      def setup_runs(analyze_id, main_worker_id)
        @run = Ai::AssistantRun.find(analyze_id)
        @main_worker = Ai::AssistantRun.find(main_worker_id)
      end

      def valid_runs?(run)
        run.status.in?(%w[queued in_progress])
      end

      def fetch_transcription_result
        transcribe_run = Ai::AssistantRun.joins(:assistant)
                                         .find_by(
                                           thread_id: @run.thread_id,
                                           ai_assistants: { purpose: 'lgd_transcriber' }
                                         )

        raise 'Transcription not completed' unless transcribe_run&.status == 'completed'

        transcribe_run.json_response['compiled_transcript']
      end

      def process_analysis(transcription_result)
        results = generate_analysis_results(transcription_result)

        @run.update!(
          status: 'completed',
          json_response: results
        )
      end

      def generate_analysis_results(transcription_result)
        analysis_result = analyze_competency(transcription_result)
        formatted_analysis_result = format_analysis_result(analysis_result)
        summarized_analysis_result = summarize_analysis_result(formatted_analysis_result)

        {
          analysis_result: analysis_result,
          formatted_analysis_result: formatted_analysis_result,
          summarized_analysis_result: summarized_analysis_result
        }
      end

      def handle_error(error)
        Sentry.capture_exception(error)
        @run.update!(
          status: 'failed',
          raw_response: error.message
        )

        raise error
      end

      def analyze_competency(transcription_result)
        assistant = Ai::Assistant.find_by!(purpose: :lgd_analyze_assistant)

        lgd_competencies = LocaleCache.new.properties['lgd_competencies']
        prompt_contexts = []

        lgd_competencies.each do |competency_obj|
          prompt_contexts << "### #{competency_obj['competency']}"

          competency_obj['levels'].each do |level_obj|
            next unless level_obj['name'].present?

            level_name = "  * Level #{level_obj['order_level']}:#{level_obj['name']}"
            prompt_contexts << level_name

            level_obj['key_behaviors'].each do |kb_obj|
              prompt_contexts << "    * #{kb_obj['name']}"
              prompt_contexts << "      * Detailed Behavior: #{kb_obj['detailed_behavior']}"
              next unless kb_obj['keywords'].present?

              formatted_keywords = kb_obj['keywords'].map { |kw| "\"#{kw}\"" }.join(', ')
              prompt_contexts << "      * Keywords: #{formatted_keywords}"
            end
          end

          prompt_contexts << ''
        end

        prompt_contexts.pop if prompt_contexts.last == ''
        prompt_contexts = prompt_contexts.join("\n")

        assistant.system_instruction = assistant.system_instruction.gsub(
          '{{lgd_competencies}}',
          prompt_contexts
        )

        process_with_assistant(assistant, transcription_result)
      end

      def format_analysis_result(analysis_result)
        assistant = Ai::Assistant.find_by!(purpose: :lgd_formatting_assistant)
        process_with_assistant(assistant, analysis_result)
      end

      def summarize_analysis_result(analysis_result)
        assistant = Ai::Assistant.find_by!(purpose: :lgd_summary_assistant)
        process_with_assistant(assistant, analysis_result)
      end

      def process_with_assistant(assistant, input_data)
        params = {
          thread_id: @run.thread_id,
          ai_assistant_id: assistant.id,
          user_id: @run.user_id
        }

        runner = Ai::AssistantRun.find_or_initialize_by(params)
        return extract_result(runner) if runner.present? && runner.completed?

        runner.status = 'in_progress'
        runner.save!

        run_gen_ai(assistant, runner, input_data)

        extract_result(runner)
      end

      def extract_result(runner)
        result = runner.json_response
        (result.is_a?(Hash) && result.key?('result')) ? result['result'] : result
      end

      def run_gen_ai(assistant, runner, user_prompt)
        formatted_prompt = prepare_prompt(assistant, user_prompt)
        ai_params = prepare_ai_params(assistant, formatted_prompt)

        response = vertex_ai_service.generate_content(ai_params)
        raw_responses = response
        text_response = response.dig('candidates', 0, 'content', 'parts', 0, 'text')
        candidate_token = response.dig('usageMetadata', 'totalTokenCount') || 0

        update_runner(runner, raw_responses, ai_params, candidate_token, text_response)
      end

      def prepare_prompt(assistant, user_prompt)
        <<~PROMPT
          #{assistant.content}

          #{user_prompt}
        PROMPT
      end

      def prepare_ai_params(assistant, prompt)
        {
          model: assistant.model,
          system_instruction: {
            parts: [{ text: assistant.system_instruction }]
          },
          contents: [
            {
              role: 'user',
              parts: [{ text: prompt }]
            }
          ],
          generation_config: {
            temperature: assistant.temperature,
            response_mime_type: assistant.response_mime_type
          }
        }
      end

      def update_runner(runner, raw_responses, ai_params, token_count, responses)
        parsed_response = {}
        begin
          parsed_response = JSON.parse(responses)
        rescue JSON::ParserError
          parsed_response = { 'result' => responses }
        end

        runner.update!(
          status: 'completed',
          raw_response: raw_responses,
          request_raw: ai_params,
          total_token: token_count,
          json_response: parsed_response
        )
      end

      def vertex_ai_service
        ::External::VertexAiService.new
      end
    end
  end
end
