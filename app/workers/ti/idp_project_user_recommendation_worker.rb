# frozen_string_literal: true

module Ti
  class IdpProjectUserRecommendationWorker < ApplicationWorker
    def perform(id)
      query = <<~SQL.squish
        SELECT user_id,
          competency_category AS competency_name,
          100 - competency_match AS gap_percentage
        FROM mart_employee_competency_gap_internal
        WHERE competency_match < 100
          AND user_id = :user_id
      SQL

      @tipu = ::Ti::IdpProjectUser.find(id)
      @tipu.update!(status: :in_progress)

      project = @tipu.project
      user_id = @tipu.user_id
      sanitized_sql = ActiveRecord::Base.sanitize_sql [query, { user_id: }]
      competency_gaps = data_mart.query(sanitized_sql).to_a

      role_attribute = PartnerAttribute.find_by(
        key: :role,
        partner_id: project.partner_id
      )

      upa = UserPartnerAttribute.find_by(
        user_id: @tipu.user_id,
        partner_attribute_id: role_attribute.id
      )

      role_name = upa&.string_value.presence
      role_name ||= @tipu.user.job_title

      objectives = generate_objective_statements(
        competency_gaps
      )

      ai_recommendation = generate_ai_recommendation(
        role_name,
        competency_gaps,
        objectives
      )

      @tipu.update!(
        competency_gaps:,
        objectives:,
        ai_recommendation:,
        status: :done
      )
    rescue StandardError => e
      Sentry.capture_exception(e)
      @tipu.update!(status: :failed)
    end

    private

    def data_mart
      @data_mart ||= ::Ti::BigqueryClientService.new.data_mart
    end

    def gemini_service
      ::External::GoogleAiService.new
    end

    def generate_objective_statements(competency_gaps)
      competency_gaps.filter_map do |competency_gap|
        ai_prompt = ::Ai::Prompt.find_by(name: :idp_objective_generator)
        prompt = ai_prompt.message.dup.gsub(
          '{{competency_gap}}',
          competency_gap.to_json
        )

        result = gemini_service.generate_content(
          {
            model: ai_prompt.model,
            contents: [
              {
                role: :user,
                parts: [{ text: prompt }]
              }
            ],
            generationConfig: {
              responseMimeType: 'application/json',
              temperature: ai_prompt.temperature
            }
          }
        )

        parsed_result = safe_parse_json(
          result['candidates'].first['content']['parts'].first['text']
        )

        parsed_result['objective_statement'].presence
      end
    end

    def generate_ai_recommendation(role_name, competency_gaps, objectives)
      ai_prompt = ::Ai::Prompt.find_by(name: :idp_ai_recommendation)
      prompt = ai_prompt.message.gsub(
        '{{role_name}}',
        role_name.to_json
      ).gsub(
        '{{competency_gaps}}',
        competency_gaps.to_json
      ).gsub(
        '{{objectives}}',
        objectives.to_json
      )

      result = gemini_service.generate_content(
        {
          model: ai_prompt.model,
          contents: [
            {
              role: :user,
              parts: [{ text: prompt }]
            }
          ],
          generationConfig: {
            responseMimeType: 'application/json',
            temperature: ai_prompt.temperature
          }
        }
      )

      safe_parse_json(
        result['candidates'].first['content']['parts'].first['text']
      )
    end
  end
end
