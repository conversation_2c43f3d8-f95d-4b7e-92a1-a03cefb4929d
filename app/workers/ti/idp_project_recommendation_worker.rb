# frozen_string_literal: true

module Ti
  class IdpProjectRecommendationWorker < ApplicationWorker
    def perform(project_id)
      @project = ::Ti::IdpProject.find(project_id)
      @project.update!(status: :in_progress)

      @project.ti_idp_project_users.each do |ti_idp_project_user|
        ::Ti::IdpProjectUserRecommendationWorker.perform_async(
          ti_idp_project_user.id
        )
      end

      all_count = @project.ti_idp_project_users.count
      timeout = 15.minutes.from_now

      while true
        sleep 10.seconds
        break if timeout.past?

        sql = <<~SQL.squish
          SELECT COUNT(*) AS finished_count
          FROM ti_idp_project_users
          WHERE discarded_at IS NULL
            AND ti_idp_project_id = :project_id
            AND status IN(:finished_statuses)
        SQL

        sanitized_sql = ActiveRecord::Base.sanitize_sql [
          sql,
          { project_id:, finished_statuses: %i[done failed]}
        ]

        result = ActiveRecord::Base.connection.execute(sanitized_sql)
        count = result.first['finished_count'].to_i
        break if count >= all_count
      end

      @project.update!(status: :done)
    rescue StandardError => e
      Sentry.capture_exception(e)
      @project.update!(status: :failed)
    end
  end
end
