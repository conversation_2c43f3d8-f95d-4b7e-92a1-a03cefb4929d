# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'
.idea
# Ignore bundler config.
/.bundle
/vendor

# Ignore the default SQLite database.
/db/*.sqlite3
/db/*.sqlite3-journal
/db/*.sqlite3-*

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore uploaded files in development.
/storage/*
!/storage/.keep

/public/
/public/assets
.byebug_history

# Ignore master key for decrypting credentials and more.
/config/master.key

/public
/public/packs
/public/packs-test
/node_modules
/yarn-error.log
yarn-debug.log*
.yarn-integrity
.DS_Store

# Ignore database.yml
/config/database.yml

# Ignore smtp.yml
/config/smtp.yml

# Ignore redis.yml
/config/redis.yml

# Ignore application configuration
/config/application.yml
/config/scout_apm.yml
/config/honeybadger.yml
/config/gcp_admin.json
.swp

# ignore local version manager
.tool-versions
.mise.toml
